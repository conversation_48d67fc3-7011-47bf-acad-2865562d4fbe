# 被执行人公司与组织机构校验规则

## 项目概述

本项目基于Apache Flink实现被执行人公司与组织机构代码的校验规则，解决公司名称与组织机构代码匹配的问题。严格按照企查查标准清洗流程实现。

## 业务背景

根据企查查数据清洗需求，解决以下核心问题：
1. **多keyno问题**：通过公司名称获取到多个keyno
2. **keyno不匹配问题**：获取到唯一keyno，但与组织机构代码对不上
3. **名称不完整问题**：因为名称中缺少地区信息（如"河南省"、"市"）导致无法获取keyno

## 技术架构

### 标准清洗流程
```
MainJob → MainStep → SpiderStep/DapStep → CleanStep
```

### 详细流程说明
```
1. MainJob: 程序入口，初始化环境和参数
2. MainStep: 主流程控制，获取数据源并分发到不同处理步骤
3. SpiderStep: 爬虫数据处理流程（8个标准步骤）
4. DapStep: DAP数据处理流程（4个标准步骤）
5. CleanStep: 清洗逻辑处理流程（6个标准步骤）
```

### 核心组件

#### 1. MainJob主类
- **类名**: `CompanyOrgValidationMainJob`
- **功能**: 程序入口点，按照标准流程初始化环境和参数

#### 2. MainStep主流程
- **类名**: `MainStep`
- **继承**: `MainParentStep`
- **功能**:
  - 获取爬虫数据源（当前使用测试数据）
  - 获取DAP数据源（当前业务不需要）
  - 分发到SpiderStep和DapStep处理

#### 3. SpiderStep爬虫处理步骤
- **类名**: `SpiderStep`
- **继承**: `SpiderParentStep<CompanyInfo, ValidationResult>`
- **标准8步骤**:
  1. `transformSpiderStringToObjectFlatMap()` - 字符串转对象
  2. `saveSpiderToMongoDB()` - 保存到MongoDB（跳过）
  3. `cleanSpiderFieldFlatMap()` - 数据预处理和清洗
  4. `queryCompanyKeyNoFunction()` - 查询公司KeyNo
  5. `queryPersonKeyNoFunction()` - 查询个人KeyNo（跳过）
  6. `mergeSpiderAndDapFunction()` - 合并数据（跳过）
  7. `cleanStep()` - 调用清洗步骤
  8. `saveBaseDB()` - 保存到基础数据库（跳过）

#### 4. CleanStep清洗步骤
- **类名**: `CleanStep`
- **继承**: `CleanParentStep<CompanyInfo, ValidationResult>`
- **标准6步骤**:
  1. `transformBaseObjectToProdObjectFlatMap()` - 核心校验逻辑
  2. `queryCourtInfoFunction()` - 查询法院信息（跳过）
  3. `pushRiskMessageFunction()` - 推送风险消息（跳过）
  4. `saveBusinessDataFunction()` - 保存业务数据（跳过）
  5. `cleanAndSaveDetailDataFunction()` - 输出校验结果
  6. `cleanAndRefreshCountDataFunction()` - 统计数据（跳过）

#### 5. DapStep DAP处理步骤
- **类名**: `DapStep`
- **继承**: `DapParentStep<Object>`
- **标准4步骤**: 全部跳过（当前业务不需要DAP数据）

### 数据模型

#### CompanyInfo（输入数据模型）
```java
- companyName: String        // 公司名称
- originalOrgCode: String    // 原始组织机构代码
- dataSource: String         // 数据来源标识
- recordId: String          // 记录ID
```

#### ValidationResult（输出数据模型）
```java
- originalCompanyInfo: CompanyInfo  // 原始公司信息
- matchedKeyno: String             // 匹配到的keyno
- matchType: MatchType             // 匹配类型（精确/相似/未找到/多个）
- matchedFourCode: FourCodeInfo    // 匹配的四码信息
- confidenceScore: double          // 置信度分数
- validationStatus: ValidationStatus // 校验状态
- message: String                  // 错误信息或备注
```

#### FourCodeInfo（四码信息模型）
```java
- creditCode: String        // 统一信用代码
- orgCode: String          // 组织机构代码
- businessRegNo: String    // 工商注册号
- taxNo: String           // 纳税人识别号
- keyno: String           // 关联的keyno
```

## 校验规则

### 1. 数据预处理规则
- 清洗空格和标点符号
- 统一转换为大写
- 过滤长度≤6的代码
- 过滤弱智数字（如：123456、*********）
- 过滤纯中文代码
- 过滤全零代码

### 2. 匹配优先级
1. **精确匹配**：组织机构代码完全一致
2. **相似匹配**：去除脱敏字符（*）后顺序匹配
3. **多匹配处理**：当同时匹配多个keyno时，输出所有候选结果

### 3. 相似匹配算法
- 去除输入代码中的脱敏字符（*）
- 检查剩余字符是否能在目标代码中按顺序找到
- 示例：`32058400****0923` 与 `320584000120923` 匹配成功

## 项目结构

```
src/main/java/com/kinsha/validation/
├── job/                                  # 主程序
│   └── CompanyOrgValidationMainJob.java # 主程序入口
├── step/                                 # 标准流程步骤
│   ├── StepConstructorParam.java        # 步骤构造参数
│   ├── MainParentStep.java              # 主流程父类
│   ├── SpiderParentStep.java            # 爬虫处理父类
│   ├── CleanParentStep.java             # 清洗步骤父类
│   ├── DapParentStep.java               # DAP处理父类
│   ├── MainStep.java                    # 主流程实现
│   ├── SpiderStep.java                  # 爬虫处理实现
│   ├── CleanStep.java                   # 清洗步骤实现
│   └── DapStep.java                     # DAP处理实现
├── model/                                # 数据模型
│   ├── CompanyInfo.java                  # 公司信息模型
│   ├── ValidationResult.java            # 校验结果模型
│   └── FourCodeInfo.java                # 四码信息模型
└── service/                              # 业务服务
    ├── KeynoService.java                # Keyno查询服务
    └── FourCodeService.java             # 四码查询服务
```

## 运行环境

### 依赖要求
- Java 8+
- Apache Flink 1.17.1
- Maven 3.6+

### 编译运行
```bash
# 编译项目
mvn clean compile

# 打包项目
mvn clean package

# 运行程序
java -cp target/company-org-validation-1.0.0.jar com.kinsha.validation.job.CompanyOrgValidationMainJob
```

## 测试数据

项目包含15个测试用例，覆盖以下场景：
1. 精确匹配（组织机构代码完全一致）
2. 相似匹配（部分脱敏字符）
3. 多keyno匹配
4. 无效数据过滤
5. 边界条件测试

## TODO事项

### 数据源相关
- [ ] 替换测试数据源为真实数据库连接
- [ ] 实现Kafka数据源支持
- [ ] 添加数据源配置管理

### 业务逻辑相关
- [ ] 实现真实的keyno查询接口
- [ ] 完善四码数据库查询逻辑
- [ ] 优化相似匹配算法性能
- [ ] 添加NLP处理模块

### 输出相关
- [ ] 实现数据库结果输出
- [ ] 实现文件输出功能
- [ ] 实现Kafka结果输出
- [ ] 添加结果统计和监控

### 性能优化
- [ ] 添加缓存机制
- [ ] 优化并行度配置
- [ ] 实现检查点和容错机制

## 版本历史

详见 [CHANGELOG.md](CHANGELOG.md)

## 联系方式

- 数据团队：吴子斌
- 清洗团队：刘龙辉
- 开发团队：企查查数据清洗团队
