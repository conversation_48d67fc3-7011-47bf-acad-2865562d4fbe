# 被执行人公司与组织机构校验规则

## 项目概述

本项目基于Apache Flink实现被执行人公司与组织机构代码的校验规则，解决公司名称与组织机构代码匹配的问题。

## 业务背景

根据企查查数据清洗需求，解决以下核心问题：
1. **多keyno问题**：通过公司名称获取到多个keyno
2. **keyno不匹配问题**：获取到唯一keyno，但与组织机构代码对不上  
3. **名称不完整问题**：因为名称中缺少地区信息（如"河南省"、"市"）导致无法获取keyno

## 技术架构

### 系统流程
```
数据源 → 数据预处理 → 校验逻辑 → 结果输出
Source → StepFunction → StepFunction → Sink
```

### 核心组件

#### 1. MainJob主类
- **类名**: `CompanyOrgValidationJob`
- **功能**: 程序入口点，配置Flink执行环境和数据流水线

#### 2. Source组件
- **类名**: `CompanyInfoSource`
- **功能**: 从数据源读取公司名称和组织机构代码信息
- **当前实现**: 提供测试数据（TODO: 后期替换为真实数据源）

#### 3. StepFunction组件

##### 数据预处理函数
- **类名**: `DataPreprocessFunction`
- **功能**: 
  - 清洗空格、标点符号
  - 统一大小写转换
  - 过滤无效数据（长度≤6的认定为无效code）
  - 过滤弱智数字、纯中文等无效数据

##### 校验函数
- **类名**: `CompanyValidationFunction`
- **功能**:
  - 根据公司名称获取keyno
  - 四码校验（统一信用代码、组织机构代码、工商注册号、纳税人识别号）
  - 相似匹配算法（去除脱敏字符后顺序匹配）
  - 多keyno冲突处理

#### 4. Sink组件
- **类名**: `ValidationResultSink`
- **功能**: 将处理结果写入目标存储
- **当前实现**: 控制台输出（TODO: 后期扩展为数据库、文件、Kafka等）

### 数据模型

#### CompanyInfo（输入数据模型）
```java
- companyName: String        // 公司名称
- originalOrgCode: String    // 原始组织机构代码
- dataSource: String         // 数据来源标识
- recordId: String          // 记录ID
```

#### ValidationResult（输出数据模型）
```java
- originalCompanyInfo: CompanyInfo  // 原始公司信息
- matchedKeyno: String             // 匹配到的keyno
- matchType: MatchType             // 匹配类型（精确/相似/未找到/多个）
- matchedFourCode: FourCodeInfo    // 匹配的四码信息
- confidenceScore: double          // 置信度分数
- validationStatus: ValidationStatus // 校验状态
- message: String                  // 错误信息或备注
```

#### FourCodeInfo（四码信息模型）
```java
- creditCode: String        // 统一信用代码
- orgCode: String          // 组织机构代码
- businessRegNo: String    // 工商注册号
- taxNo: String           // 纳税人识别号
- keyno: String           // 关联的keyno
```

## 校验规则

### 1. 数据预处理规则
- 清洗空格和标点符号
- 统一转换为大写
- 过滤长度≤6的代码
- 过滤弱智数字（如：123456、*********）
- 过滤纯中文代码
- 过滤全零代码

### 2. 匹配优先级
1. **精确匹配**：组织机构代码完全一致
2. **相似匹配**：去除脱敏字符（*）后顺序匹配
3. **多匹配处理**：当同时匹配多个keyno时，输出所有候选结果

### 3. 相似匹配算法
- 去除输入代码中的脱敏字符（*）
- 检查剩余字符是否能在目标代码中按顺序找到
- 示例：`32058400****0923` 与 `320584000120923` 匹配成功

## 项目结构

```
src/main/java/com/kinsha/validation/
├── CompanyOrgValidationJob.java          # 主程序入口
├── model/                                # 数据模型
│   ├── CompanyInfo.java                  # 公司信息模型
│   ├── ValidationResult.java            # 校验结果模型
│   └── FourCodeInfo.java                # 四码信息模型
├── source/                               # 数据源
│   └── CompanyInfoSource.java           # 公司信息数据源
├── function/                             # 处理函数
│   ├── DataPreprocessFunction.java      # 数据预处理函数
│   └── CompanyValidationFunction.java   # 校验函数
├── service/                              # 业务服务
│   ├── KeynoService.java               # Keyno查询服务
│   └── FourCodeService.java            # 四码查询服务
└── sink/                                # 输出组件
    └── ValidationResultSink.java       # 校验结果输出
```

## 运行环境

### 依赖要求
- Java 8+
- Apache Flink 1.17.1
- Maven 3.6+

### 编译运行
```bash
# 编译项目
mvn clean compile

# 打包项目
mvn clean package

# 运行程序
java -cp target/company-org-validation-1.0.0.jar com.kinsha.validation.CompanyOrgValidationJob
```

## 测试数据

项目包含15个测试用例，覆盖以下场景：
1. 精确匹配（组织机构代码完全一致）
2. 相似匹配（部分脱敏字符）
3. 多keyno匹配
4. 无效数据过滤
5. 边界条件测试

## TODO事项

### 数据源相关
- [ ] 替换测试数据源为真实数据库连接
- [ ] 实现Kafka数据源支持
- [ ] 添加数据源配置管理

### 业务逻辑相关
- [ ] 实现真实的keyno查询接口
- [ ] 完善四码数据库查询逻辑
- [ ] 优化相似匹配算法性能
- [ ] 添加NLP处理模块

### 输出相关
- [ ] 实现数据库结果输出
- [ ] 实现文件输出功能
- [ ] 实现Kafka结果输出
- [ ] 添加结果统计和监控

### 性能优化
- [ ] 添加缓存机制
- [ ] 优化并行度配置
- [ ] 实现检查点和容错机制

## 版本历史

详见 [CHANGELOG.md](CHANGELOG.md)

## 联系方式

- 数据团队：吴子斌
- 清洗团队：刘龙辉
- 开发团队：企查查数据清洗团队
