package com.kinsha.confluence;

import org.apache.hadoop.hive.ql.exec.UDF;
import org.apache.hadoop.hive.ql.exec.Description;
import org.apache.hadoop.hive.ql.exec.UDFArgumentException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.StringObjectInspector;
import org.apache.hadoop.io.Text;

import java.util.*;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * 案由解析公告方法优化UDF
 * 解决文档中提到的案由解析问题：
 * 1. 优先级处理：正文 > 标题 > 采集
 * 2. 泛化案由特殊优先级：合同纠纷等需要更具体化
 * 3. 关键词冲突解决：避免包含关系导致的随机选择
 * 4. 数据质量校验：修复case_reason为空但标准化字段不为空的问题
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Description(name = "parse_case_reason", 
            value = "_FUNC_(title, content, original_reason, case_type) - 优化案由解析，返回标准化案由",
            extended = "Example: SELECT parse_case_reason(title, content, original_reason, case_type) FROM table")
public class CaseReasonParserUDF extends UDF {
    
    private static final Map<String, String> CASE_REASON_MAPPING = new HashMap<>();
    private static final Map<String, List<String>> CASE_REASON_KEYWORDS = new HashMap<>();
    private static final Set<String> GENERALIZED_REASONS = new HashSet<>();
    private static final Pattern EXECUTION_PATTERN = Pattern.compile(".*执行.*");
    
    // 初始化案由映射表和关键词
    static {
        initializeCaseReasonMapping();
        initializeCaseReasonKeywords();
        initializeGeneralizedReasons();
    }
    
    /**
     * 主解析方法
     * @param title 案件标题
     * @param content 案件正文
     * @param originalReason 原始案由
     * @param caseType 案件类型
     * @return 标准化案由
     */
    public Text evaluate(Text title, Text content, Text originalReason, Text caseType) {
        if (title == null && content == null && originalReason == null) {
            return null;
        }
        
        try {
            String titleStr = title != null ? title.toString() : "";
            String contentStr = content != null ? content.toString() : "";
            String originalReasonStr = originalReason != null ? originalReason.toString() : "";
            String caseTypeStr = caseType != null ? caseType.toString() : "";
            
            String result = parseCaseReason(titleStr, contentStr, originalReasonStr, caseTypeStr);
            return new Text(result);
        } catch (Exception e) {
            // 记录错误日志，返回原始案由
            System.err.println("案由解析错误: " + e.getMessage());
            return originalReason != null ? originalReason : new Text("");
        }
    }
    
    /**
     * 核心案由解析逻辑
     */
    private String parseCaseReason(String title, String content, String originalReason, String caseType) {
        // 1. 优先级解析：正文 > 标题 > 采集
        String contentReason = extractCaseReasonFromText(content, caseType);
        String titleReason = extractCaseReasonFromText(title, caseType);
        String originalReasonStandard = standardizeCaseReason(originalReason);
        
        // 2. 泛化案由特殊优先级处理
        String result = handleGeneralizedReasonPriority(contentReason, titleReason, originalReasonStandard);
        
        // 3. 执行类案件特殊处理
        if (isExecutionCase(caseType, title, content)) {
            result = addExecutionSuffix(result);
        }
        
        // 4. 数据质量校验和修复
        result = validateAndFixCaseReason(result, originalReason, contentReason, titleReason);
        
        return result;
    }
    
    /**
     * 从文本中提取案由
     */
    private String extractCaseReasonFromText(String text, String caseType) {
        if (text == null || text.trim().isEmpty()) {
            return "";
        }
        
        // 按优先级排序的关键词匹配
        List<CaseReasonMatch> matches = new ArrayList<>();
        
        for (Map.Entry<String, List<String>> entry : CASE_REASON_KEYWORDS.entrySet()) {
            String standardReason = entry.getKey();
            List<String> keywords = entry.getValue();
            
            for (String keyword : keywords) {
                if (text.contains(keyword)) {
                    // 计算匹配度：关键词长度 + 位置权重
                    int matchScore = calculateMatchScore(text, keyword, standardReason);
                    matches.add(new CaseReasonMatch(standardReason, keyword, matchScore));
                }
            }
        }
        
        // 按匹配度排序，选择最佳匹配
        if (!matches.isEmpty()) {
            matches.sort((a, b) -> Integer.compare(b.score, a.score));
            return matches.get(0).standardReason;
        }
        
        return "";
    }
    
    /**
     * 计算匹配度分数
     */
    private int calculateMatchScore(String text, String keyword, String standardReason) {
        int score = keyword.length() * 10; // 基础分数
        
        // 位置权重：标题中的匹配权重更高
        int titleIndex = text.indexOf(keyword);
        if (titleIndex >= 0 && titleIndex < text.length() * 0.3) { // 在前30%位置
            score += 50;
        }
        
        // 精确匹配权重
        if (text.contains(keyword + "纠纷") || text.contains(keyword + "案件")) {
            score += 30;
        }
        
        // 避免包含关系冲突：选择更具体的案由
        if (standardReason.length() > keyword.length()) {
            score += 20;
        }
        
        return score;
    }
    
    /**
     * 泛化案由特殊优先级处理
     */
    private String handleGeneralizedReasonPriority(String contentReason, String titleReason, String originalReason) {
        // 如果正文解析出泛化案由，尝试从标题获取更具体的案由
        if (GENERALIZED_REASONS.contains(contentReason)) {
            if (!titleReason.isEmpty() && titleReason.length() > contentReason.length()) {
                return titleReason;
            }
        }
        
        // 如果标题解析出泛化案由，尝试从原始案由获取更具体的案由
        if (GENERALIZED_REASONS.contains(titleReason)) {
            if (!originalReason.isEmpty() && originalReason.length() > titleReason.length()) {
                return originalReason;
            }
        }
        
        // 优先级：正文 > 标题 > 原始
        if (!contentReason.isEmpty()) {
            return contentReason;
        } else if (!titleReason.isEmpty()) {
            return titleReason;
        } else {
            return originalReason;
        }
    }
    
    /**
     * 判断是否为执行类案件
     */
    private boolean isExecutionCase(String caseType, String title, String content) {
        if (caseType != null && caseType.contains("执行")) {
            return true;
        }
        
        String combinedText = (title + " " + content).toLowerCase();
        return EXECUTION_PATTERN.matcher(combinedText).matches();
    }
    
    /**
     * 为执行类案件添加"执行"后缀
     */
    private String addExecutionSuffix(String caseReason) {
        if (caseReason == null || caseReason.isEmpty()) {
            return "执行";
        }
        
        if (!caseReason.endsWith("执行")) {
            return caseReason + "执行";
        }
        
        return caseReason;
    }
    
    /**
     * 数据质量校验和修复
     */
    private String validateAndFixCaseReason(String result, String originalReason, String contentReason, String titleReason) {
        // 修复case_reason为空但标准化字段不为空的问题
        if ((originalReason == null || originalReason.trim().isEmpty()) && 
            (!contentReason.isEmpty() || !titleReason.isEmpty())) {
            return result;
        }
        
        // 避免返回不标准的案由
        if (result != null && (result.contains("其他") || result.length() <= 2)) {
            // 尝试从其他来源获取更具体的案由
            if (!contentReason.isEmpty() && !contentReason.contains("其他")) {
                return contentReason;
            }
            if (!titleReason.isEmpty() && !titleReason.contains("其他")) {
                return titleReason;
            }
        }
        
        return result;
    }
    
    /**
     * 标准化案由
     */
    private String standardizeCaseReason(String originalReason) {
        if (originalReason == null || originalReason.trim().isEmpty()) {
            return "";
        }
        
        return CASE_REASON_MAPPING.getOrDefault(originalReason.trim(), originalReason.trim());
    }
    
    /**
     * 初始化案由映射表
     */
    private static void initializeCaseReasonMapping() {
        // 基于文档中的案由映射表
        CASE_REASON_MAPPING.put("其他", "");
        CASE_REASON_MAPPING.put("其他合同纠纷", "合同纠纷");
        CASE_REASON_MAPPING.put("其他侵权纠纷", "侵权纠纷");
        CASE_REASON_MAPPING.put("罚金", "");
        CASE_REASON_MAPPING.put("买卖合同纠纷", "买卖合同纠纷");
        CASE_REASON_MAPPING.put("金融借款合同纠纷", "金融借款合同纠纷");
        CASE_REASON_MAPPING.put("民间借贷纠纷", "民间借贷纠纷");
        CASE_REASON_MAPPING.put("房屋租赁合同纠纷", "房屋租赁合同纠纷");
        CASE_REASON_MAPPING.put("著作权侵权纠纷", "著作权侵权纠纷");
        CASE_REASON_MAPPING.put("船舶碰撞纠纷", "船舶碰撞纠纷");
        CASE_REASON_MAPPING.put("不履行法定职责", "不履行法定职责");
        CASE_REASON_MAPPING.put("排除妨碍纠纷", "排除妨碍纠纷");
        CASE_REASON_MAPPING.put("第三人撤销之诉", "第三人撤销之诉");
        CASE_REASON_MAPPING.put("申请确认仲裁协议效力", "申请确认仲裁协议效力");
        CASE_REASON_MAPPING.put("诈骗罪", "诈骗罪");
        CASE_REASON_MAPPING.put("危险驾驶罪", "危险驾驶罪");
    }
    
    /**
     * 初始化案由关键词
     */
    private static void initializeCaseReasonKeywords() {
        // 买卖合同纠纷关键词
        CASE_REASON_KEYWORDS.put("买卖合同纠纷", Arrays.asList("买卖合同", "购销合同", "销售合同"));
        
        // 金融借款合同纠纷关键词
        CASE_REASON_KEYWORDS.put("金融借款合同纠纷", Arrays.asList("金融借款", "银行贷款", "借款合同"));
        
        // 民间借贷纠纷关键词
        CASE_REASON_KEYWORDS.put("民间借贷纠纷", Arrays.asList("民间借贷", "个人借款", "私人借款"));
        
        // 房屋租赁合同纠纷关键词
        CASE_REASON_KEYWORDS.put("房屋租赁合同纠纷", Arrays.asList("房屋租赁", "租房合同", "租赁合同"));
        
        // 著作权侵权纠纷关键词
        CASE_REASON_KEYWORDS.put("著作权侵权纠纷", Arrays.asList("著作权", "版权侵权", "知识产权"));
        
        // 诈骗罪关键词
        CASE_REASON_KEYWORDS.put("诈骗罪", Arrays.asList("诈骗", "欺诈", "骗取"));
        
        // 危险驾驶罪关键词
        CASE_REASON_KEYWORDS.put("危险驾驶罪", Arrays.asList("危险驾驶", "醉驾", "酒驾"));
        
        // 合同纠纷（泛化）
        CASE_REASON_KEYWORDS.put("合同纠纷", Arrays.asList("合同", "协议", "约定"));
    }
    
    /**
     * 初始化泛化案由集合
     */
    private static void initializeGeneralizedReasons() {
        GENERALIZED_REASONS.add("合同纠纷");
        GENERALIZED_REASONS.add("侵权纠纷");
        GENERALIZED_REASONS.add("其他");
    }
    
    /**
     * 案由匹配结果类
     */
    private static class CaseReasonMatch {
        String standardReason;
        String keyword;
        int score;
        
        CaseReasonMatch(String standardReason, String keyword, int score) {
            this.standardReason = standardReason;
            this.keyword = keyword;
            this.score = score;
        }
    }
}