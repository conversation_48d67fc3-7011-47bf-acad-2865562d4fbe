package com.kinsha.validation.source;

import com.kinsha.validation.model.CompanyInfo;
import org.apache.flink.streaming.api.functions.source.SourceFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;

/**
 * 公司信息数据源
 * 提供测试数据，模拟从外部数据源读取公司名称和组织机构代码信息
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class CompanyInfoSource implements SourceFunction<CompanyInfo> {
    
    private static final Logger logger = LoggerFactory.getLogger(CompanyInfoSource.class);
    private static final long serialVersionUID = 1L;
    
    private volatile boolean isRunning = true;
    
    @Override
    public void run(SourceContext<CompanyInfo> ctx) throws Exception {
        logger.info("开始生成测试数据...");
        
        // TODO: 测试数据，后期需要替换为真实数据源
        List<CompanyInfo> testData = createTestData();
        
        for (CompanyInfo companyInfo : testData) {
            if (!isRunning) {
                break;
            }
            
            logger.info("发送数据: {}", companyInfo);
            ctx.collect(companyInfo);
            
            // 模拟数据流间隔
            Thread.sleep(1000);
        }
        
        logger.info("测试数据发送完成");
    }
    
    @Override
    public void cancel() {
        logger.info("取消数据源");
        isRunning = false;
    }
    
    /**
     * 创建测试数据
     * TODO: 这些是测试数据，需要根据实际业务场景替换
     * @return 测试数据列表
     */
    private List<CompanyInfo> createTestData() {
        return Arrays.asList(
            // 测试案例1：精确匹配 - 组织机构代码完全一致
            new CompanyInfo("阿拉善盟旭阳煤业有限公司", "57885902-2", "test_source", "001"),
            
            // 测试案例2：精确匹配 - 统一信用代码完全一致  
            new CompanyInfo("安徽力天建设有限公司", "75488432-4", "test_source", "002"),
            
            // 测试案例3：相似匹配 - 统一信用代码部分脱敏
            new CompanyInfo("安徽明珠建设工程有限公司", "9134120079****185J", "test_source", "003"),
            
            // 测试案例4：相似匹配 - 组织机构代码部分脱敏
            new CompanyInfo("北京科技发展有限公司", "32058400****0923", "test_source", "004"),
            
            // 测试案例5：无法匹配 - 代码不存在
            new CompanyInfo("阿荣旗那吉镇三合运输车队", "L0517650-X", "test_source", "005"),
            
            // 测试案例6：无效数据 - 代码长度过短
            new CompanyInfo("测试公司A", "458", "test_source", "006"),
            
            // 测试案例7：无效数据 - 纯数字弱智代码
            new CompanyInfo("测试公司B", "123456", "test_source", "007"),
            
            // 测试案例8：无效数据 - 全零代码
            new CompanyInfo("测试公司C", "0000", "test_source", "008"),
            
            // 测试案例9：多个匹配 - 可能匹配多个keyno的情况
            new CompanyInfo("上海贸易有限公司", "91310000123456789X", "test_source", "009"),
            
            // 测试案例10：名称不完整 - 缺少地区信息
            new CompanyInfo("明珠建设工程有限公司", "79981018-5", "test_source", "010"),
            
            // 测试案例11：空值测试
            new CompanyInfo("空代码测试公司", "", "test_source", "011"),
            
            // 测试案例12：null值测试
            new CompanyInfo("null代码测试公司", null, "test_source", "012"),
            
            // 测试案例13：包含特殊字符
            new CompanyInfo("特殊字符测试公司", "91-310-000-123456789X", "test_source", "013"),
            
            // 测试案例14：大小写混合
            new CompanyInfo("大小写测试公司", "9131000012345678x", "test_source", "014"),
            
            // 测试案例15：长度边界测试
            new CompanyInfo("边界测试公司", "123456", "test_source", "015")
        );
    }
}
