package com.kinsha.validation.job;

import com.kinsha.validation.step.MainStep;
import com.kinsha.validation.step.StepConstructorParam;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 被执行人公司与组织机构校验规则主程序
 * 按照标准流程实现：MainJob → MainStep → SpiderStep/DapStep → CleanStep
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class CompanyOrgValidationMainJob {
    
    private static final Logger logger = LoggerFactory.getLogger(CompanyOrgValidationMainJob.class);
    
    public static void main(String[] args) throws Exception {
        logger.info("=== 被执行人公司与组织机构校验规则程序启动 ===");
        
        try {
            // 1. 准备参数
            ParameterTool propertiesParams = createPropertiesParams();
            ParameterTool dmpParams = ParameterTool.fromArgs(args);
            
            // 2. 创建Flink执行环境
            StreamExecutionEnvironment env = createExecutionEnvironment(propertiesParams);
            
            // 3. 构建Step构造参数
            StepConstructorParam stepConstructorParam = StepConstructorParam.builder()
                    .env(env)
                    .propertiesParams(propertiesParams)
                    .dmpParams(dmpParams)
                    .build();
            
            // 4. 执行主流程
            new MainStep(stepConstructorParam).process();
            
            // 5. 启动程序执行
            logger.info("开始执行Flink作业...");
            env.execute("被执行人公司与组织机构校验规则");
            
            logger.info("=== 程序执行完成 ===");
            
        } catch (Exception e) {
            logger.error("程序执行失败", e);
            throw e;
        }
    }
    
    /**
     * 创建配置参数
     * TODO: 这里使用默认配置，实际应该从配置文件加载
     * @return 配置参数
     */
    private static ParameterTool createPropertiesParams() {
        // TODO: 实际应该从application.properties等配置文件加载
        ParameterTool.Builder builder = ParameterTool.fromMap(new java.util.HashMap<String, String>() {{
            // 基础配置
            put("app.name", "company-org-validation");
            put("app.version", "1.0.0");
            
            // 并行度配置
            put("stream.parallelism", "1");
            put("spider.process.parallelism", "1");
            put("core.pool.size", "1");
            
            // Kafka配置 (暂时不使用)
            put("spider.metrics.topic", "spider-company-topic");
            put("spider.kafka.group.id", "company-validation-group");
            put("dap.metrics.topic", "dap-company-topic");
            put("dap.kafka.group.id", "company-validation-dap-group");
            
            // 数据库配置 (暂时不使用)
            put("database.url", "**********************************************");
            put("database.username", "root");
            put("database.password", "password");
        }});
        
        return builder.build();
    }
    
    /**
     * 创建Flink执行环境
     * @param propertiesParams 配置参数
     * @return StreamExecutionEnvironment
     */
    private static StreamExecutionEnvironment createExecutionEnvironment(ParameterTool propertiesParams) {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        
        // 设置并行度
        int parallelism = propertiesParams.getInt("stream.parallelism", 1);
        env.setParallelism(parallelism);
        
        // 设置检查点（可选）
        // env.enableCheckpointing(60000); // 60秒检查点间隔
        
        // 设置重启策略（可选）
        // env.setRestartStrategy(RestartStrategies.fixedDelayRestart(3, Time.of(10, TimeUnit.SECONDS)));
        
        logger.info("Flink执行环境创建完成，并行度: {}", env.getParallelism());
        return env;
    }
}
