package com.kinsha.validation;

import com.kinsha.validation.function.CompanyValidationFunction;
import com.kinsha.validation.function.DataPreprocessFunction;
import com.kinsha.validation.model.CompanyInfo;
import com.kinsha.validation.model.ValidationResult;
import com.kinsha.validation.sink.ValidationResultSink;
import com.kinsha.validation.source.CompanyInfoSource;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 被执行人公司与组织机构校验规则主程序
 * 
 * 实现流程：
 * 1. Source: 从数据源读取公司名称和组织机构代码信息
 * 2. DataPreprocess: 数据预清洗（去除空格、标点，统一大小写，过滤无效数据）
 * 3. Validation: 核心校验逻辑（根据名称获取keyno，四码校验，相似匹配）
 * 4. Sink: 将处理结果写入目标存储
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class CompanyOrgValidationJob {
    
    private static final Logger logger = LoggerFactory.getLogger(CompanyOrgValidationJob.class);
    
    public static void main(String[] args) throws Exception {
        logger.info("=== 被执行人公司与组织机构校验规则程序启动 ===");
        
        // 1. 创建Flink执行环境
        StreamExecutionEnvironment env = createExecutionEnvironment();
        
        // 2. 构建数据处理流水线
        buildDataPipeline(env);
        
        // 3. 启动程序执行
        logger.info("开始执行Flink作业...");
        env.execute("被执行人公司与组织机构校验规则");
        
        logger.info("=== 程序执行完成 ===");
    }
    
    /**
     * 创建Flink执行环境
     * @return StreamExecutionEnvironment
     */
    private static StreamExecutionEnvironment createExecutionEnvironment() {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        
        // 设置并行度
        env.setParallelism(1); // TODO: 根据实际需求调整并行度
        
        // 设置检查点（可选）
        // env.enableCheckpointing(60000); // 60秒检查点间隔
        
        // 设置重启策略（可选）
        // env.setRestartStrategy(RestartStrategies.fixedDelayRestart(3, Time.of(10, TimeUnit.SECONDS)));
        
        logger.info("Flink执行环境创建完成，并行度: {}", env.getParallelism());
        return env;
    }
    
    /**
     * 构建数据处理流水线
     * @param env Flink执行环境
     */
    private static void buildDataPipeline(StreamExecutionEnvironment env) {
        logger.info("开始构建数据处理流水线...");
        
        // Step 1: Source - 数据源
        DataStream<CompanyInfo> sourceStream = env
                .addSource(new CompanyInfoSource())
                .name("CompanyInfoSource")
                .uid("company-info-source");
        
        logger.info("数据源配置完成");
        
        // Step 2: DataPreprocess - 数据预处理
        DataStream<CompanyInfo> preprocessedStream = sourceStream
                .map(new DataPreprocessFunction())
                .name("DataPreprocessFunction")
                .uid("data-preprocess");
        
        logger.info("数据预处理配置完成");
        
        // Step 3: Validation - 核心校验逻辑
        DataStream<ValidationResult> validationStream = preprocessedStream
                .map(new CompanyValidationFunction())
                .name("CompanyValidationFunction")
                .uid("company-validation");
        
        logger.info("校验逻辑配置完成");
        
        // Step 4: Sink - 结果输出
        validationStream
                .addSink(new ValidationResultSink())
                .name("ValidationResultSink")
                .uid("validation-result-sink");
        
        logger.info("结果输出配置完成");
        
        logger.info("数据处理流水线构建完成");
    }
}
