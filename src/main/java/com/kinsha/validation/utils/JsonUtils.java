package com.kinsha.validation.utils;

import com.kinsha.validation.model.CompanyInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * JSON处理工具类
 * 提供JSON字符串解析功能
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class JsonUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(JsonUtils.class);
    
    /**
     * 解析JSON字符串为CompanyInfo对象
     * TODO: 简化实现，实际应该使用Jackson等JSON库
     * @param jsonStr JSON字符串
     * @return CompanyInfo对象
     */
    public static CompanyInfo parseJsonToCompanyInfo(String jsonStr) {
        try {
            // 简单的JSON解析实现
            String companyName = extractJsonValue(jsonStr, "companyName");
            String originalOrgCode = extractJsonValue(jsonStr, "originalOrgCode");
            String dataSource = extractJsonValue(jsonStr, "dataSource");
            String recordId = extractJsonValue(jsonStr, "recordId");
            
            return new CompanyInfo(companyName, originalOrgCode, dataSource, recordId);
        } catch (Exception e) {
            logger.error("解析JSON失败: {}", jsonStr, e);
            return null;
        }
    }
    
    /**
     * 从JSON字符串中提取指定字段的值
     * TODO: 简化实现，实际应该使用JSON库
     * @param jsonStr JSON字符串
     * @param fieldName 字段名
     * @return 字段值
     */
    public static String extractJsonValue(String jsonStr, String fieldName) {
        String pattern = "\"" + fieldName + "\":\"([^\"]*?)\"";
        Pattern p = Pattern.compile(pattern);
        Matcher m = p.matcher(jsonStr);
        if (m.find()) {
            String value = m.group(1);
            return "null".equals(value) ? null : value;
        }
        return "";
    }
    
    /**
     * 将CompanyInfo对象转换为JSON字符串
     * TODO: 简化实现，实际应该使用JSON库
     * @param companyInfo 公司信息对象
     * @return JSON字符串
     */
    public static String companyInfoToJson(CompanyInfo companyInfo) {
        if (companyInfo == null) {
            return "{}";
        }
        
        return String.format(
            "{\"companyName\":\"%s\",\"originalOrgCode\":\"%s\",\"dataSource\":\"%s\",\"recordId\":\"%s\"}",
            nullToEmpty(companyInfo.getCompanyName()),
            nullToEmpty(companyInfo.getOriginalOrgCode()),
            nullToEmpty(companyInfo.getDataSource()),
            nullToEmpty(companyInfo.getRecordId())
        );
    }
    
    /**
     * 空值处理
     * @param value 原始值
     * @return 处理后的值
     */
    private static String nullToEmpty(String value) {
        return value == null ? "" : value;
    }
}
