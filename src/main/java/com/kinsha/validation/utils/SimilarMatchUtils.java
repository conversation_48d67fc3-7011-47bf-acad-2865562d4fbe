package com.kinsha.validation.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 相似匹配工具类
 * 提供相似匹配算法实现
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class SimilarMatchUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(SimilarMatchUtils.class);
    
    /**
     * 判断两个代码是否相似匹配
     * 规则：去掉脱敏字符后，其他字符要顺序匹配
     * 示例：32058400****0923 与 320584000120923 匹配成功
     * 
     * @param inputCode 输入代码（可能包含脱敏字符*）
     * @param targetCode 目标代码
     * @return 是否相似匹配
     */
    public static boolean isSimilarMatch(String inputCode, String targetCode) {
        if (inputCode == null || targetCode == null) {
            return false;
        }
        
        // 预处理输入代码：去除脱敏字符并转大写
        String cleanInputCode = DataCleanUtils.removeMaskingChars(inputCode.trim().toUpperCase());
        String cleanTargetCode = targetCode.trim().toUpperCase();
        
        // 如果输入代码为空，不匹配
        if (cleanInputCode.isEmpty()) {
            return false;
        }
        
        // 检查输入代码的字符是否能在目标代码中按顺序找到
        int targetIndex = 0;
        for (int i = 0; i < cleanInputCode.length(); i++) {
            char inputChar = cleanInputCode.charAt(i);
            boolean found = false;
            
            // 在目标代码中从当前位置开始查找
            for (int j = targetIndex; j < cleanTargetCode.length(); j++) {
                if (cleanTargetCode.charAt(j) == inputChar) {
                    targetIndex = j + 1;
                    found = true;
                    break;
                }
            }
            
            // 如果某个字符没找到，则不匹配
            if (!found) {
                logger.debug("相似匹配失败: 字符 '{}' 在位置 {} 未找到, 输入={}, 目标={}", 
                           inputChar, i, cleanInputCode, cleanTargetCode);
                return false;
            }
        }
        
        logger.debug("相似匹配成功: 输入={}, 目标={}", cleanInputCode, cleanTargetCode);
        return true;
    }
    
    /**
     * 计算两个字符串的相似度分数
     * 基于顺序匹配的字符比例
     * 
     * @param inputCode 输入代码
     * @param targetCode 目标代码
     * @return 相似度分数 (0.0 - 1.0)
     */
    public static double calculateSimilarityScore(String inputCode, String targetCode) {
        if (inputCode == null || targetCode == null) {
            return 0.0;
        }
        
        String cleanInputCode = DataCleanUtils.removeMaskingChars(inputCode.trim().toUpperCase());
        String cleanTargetCode = targetCode.trim().toUpperCase();
        
        if (cleanInputCode.isEmpty() || cleanTargetCode.isEmpty()) {
            return 0.0;
        }
        
        // 如果完全相同，返回1.0
        if (cleanInputCode.equals(cleanTargetCode)) {
            return 1.0;
        }
        
        // 计算顺序匹配的字符数
        int matchedChars = 0;
        int targetIndex = 0;
        
        for (int i = 0; i < cleanInputCode.length(); i++) {
            char inputChar = cleanInputCode.charAt(i);
            
            for (int j = targetIndex; j < cleanTargetCode.length(); j++) {
                if (cleanTargetCode.charAt(j) == inputChar) {
                    matchedChars++;
                    targetIndex = j + 1;
                    break;
                }
            }
        }
        
        // 相似度 = 匹配字符数 / 输入代码长度
        double similarity = (double) matchedChars / cleanInputCode.length();
        
        logger.debug("相似度计算: 输入={}, 目标={}, 匹配字符={}, 相似度={}", 
                   cleanInputCode, cleanTargetCode, matchedChars, similarity);
        
        return similarity;
    }
    
    /**
     * 检查是否达到相似匹配的阈值
     * 
     * @param inputCode 输入代码
     * @param targetCode 目标代码
     * @param threshold 相似度阈值 (0.0 - 1.0)
     * @return 是否达到阈值
     */
    public static boolean isSimilarMatchWithThreshold(String inputCode, String targetCode, double threshold) {
        double similarity = calculateSimilarityScore(inputCode, targetCode);
        return similarity >= threshold;
    }
}
