package com.kinsha.validation.utils;

import com.kinsha.validation.model.FourCodeInfo;
import com.kinsha.validation.model.ValidationResult;
import com.kinsha.validation.service.FourCodeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 校验结果工具类
 * 提供校验结果处理和输出功能
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ValidationResultUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(ValidationResultUtils.class);
    
    /**
     * 输出校验结果到控制台
     * @param result 校验结果
     */
    public static void outputValidationResult(ValidationResult result) {
        String timestamp = LocalDateTime.now().toString();
        
        System.out.println("=== 校验结果 ===");
        System.out.println("处理时间: " + timestamp);
        System.out.println("记录ID: " + result.getOriginalCompanyInfo().getRecordId());
        System.out.println("公司名称: " + result.getOriginalCompanyInfo().getCompanyName());
        System.out.println("原始组织机构代码: " + result.getOriginalCompanyInfo().getOriginalOrgCode());
        System.out.println("校验状态: " + result.getValidationStatus());
        System.out.println("匹配类型: " + result.getMatchType());
        System.out.println("置信度: " + String.format("%.2f", result.getConfidenceScore()));
        
        if (result.getMatchedKeyno() != null) {
            System.out.println("匹配的keyno: " + result.getMatchedKeyno());
        }
        
        if (result.getMatchedFourCode() != null) {
            System.out.println("匹配的四码信息:");
            System.out.println("  - 统一信用代码: " + result.getMatchedFourCode().getCreditCode());
            System.out.println("  - 组织机构代码: " + result.getMatchedFourCode().getOrgCode());
            System.out.println("  - 工商注册号: " + result.getMatchedFourCode().getBusinessRegNo());
            System.out.println("  - 纳税人识别号: " + result.getMatchedFourCode().getTaxNo());
        }
        
        if (result.getCandidateKeynos() != null && !result.getCandidateKeynos().isEmpty()) {
            System.out.println("候选keyno列表: " + result.getCandidateKeynos());
        }
        
        System.out.println("处理消息: " + result.getMessage());
        System.out.println("================");
        System.out.println();
        
        // 记录到日志
        logger.info("校验结果输出: 公司={}, 状态={}, 匹配类型={}, keyno={}", 
                   result.getOriginalCompanyInfo().getCompanyName(),
                   result.getValidationStatus(),
                   result.getMatchType(),
                   result.getMatchedKeyno());
    }
    
    /**
     * 打印多个匹配结果
     * TODO: 根据文档要求，当多个keyno都匹配时打印结果
     * @param matchedKeynos 匹配的keyno列表
     * @param inputOrgCode 输入的组织机构代码
     * @param matchType 匹配类型
     * @param fourCodeService 四码服务
     */
    public static void printMultipleMatchResults(List<String> matchedKeynos, 
                                               String inputOrgCode, 
                                               String matchType,
                                               FourCodeService fourCodeService) {
        logger.warn("=== 多个{}结果 ===", matchType);
        logger.warn("输入组织机构代码: {}", inputOrgCode);
        logger.warn("匹配到的keyno数量: {}", matchedKeynos.size());
        
        for (int i = 0; i < matchedKeynos.size(); i++) {
            String keyno = matchedKeynos.get(i);
            FourCodeInfo fourCodeInfo = fourCodeService.getFourCodeByKeyno(keyno);
            
            logger.warn("候选{}. keyno: {}", (i + 1), keyno);
            if (fourCodeInfo != null) {
                logger.warn("  - 统一信用代码: {}", fourCodeInfo.getCreditCode());
                logger.warn("  - 组织机构代码: {}", fourCodeInfo.getOrgCode());
                logger.warn("  - 工商注册号: {}", fourCodeInfo.getBusinessRegNo());
                logger.warn("  - 纳税人识别号: {}", fourCodeInfo.getTaxNo());
            }
        }
        logger.warn("=== 多个{}结果结束 ===", matchType);
    }
    
    /**
     * 创建失败的校验结果
     * @param companyInfo 公司信息
     * @param message 失败消息
     * @return 校验结果
     */
    public static ValidationResult createFailedResult(com.kinsha.validation.model.CompanyInfo companyInfo, String message) {
        ValidationResult result = new ValidationResult(companyInfo);
        result.setValidationStatus(ValidationResult.ValidationStatus.FAILED);
        result.setMatchType(ValidationResult.MatchType.NOT_FOUND);
        result.setConfidenceScore(0.0);
        result.setMessage(message);
        return result;
    }
    
    /**
     * 创建成功的校验结果
     * @param companyInfo 公司信息
     * @param keyno 匹配的keyno
     * @param fourCodeInfo 四码信息
     * @param matchType 匹配类型
     * @param confidenceScore 置信度
     * @param message 消息
     * @return 校验结果
     */
    public static ValidationResult createSuccessResult(com.kinsha.validation.model.CompanyInfo companyInfo,
                                                     String keyno,
                                                     FourCodeInfo fourCodeInfo,
                                                     ValidationResult.MatchType matchType,
                                                     double confidenceScore,
                                                     String message) {
        ValidationResult result = new ValidationResult(companyInfo);
        result.setMatchedKeyno(keyno);
        result.setMatchedFourCode(fourCodeInfo);
        result.setMatchType(matchType);
        result.setValidationStatus(ValidationResult.ValidationStatus.SUCCESS);
        result.setConfidenceScore(confidenceScore);
        result.setMessage(message);
        return result;
    }
    
    /**
     * 创建需要人工处理的校验结果
     * @param companyInfo 公司信息
     * @param candidateKeynos 候选keyno列表
     * @param matchType 匹配类型
     * @param confidenceScore 置信度
     * @param message 消息
     * @return 校验结果
     */
    public static ValidationResult createManualResult(com.kinsha.validation.model.CompanyInfo companyInfo,
                                                    List<String> candidateKeynos,
                                                    ValidationResult.MatchType matchType,
                                                    double confidenceScore,
                                                    String message) {
        ValidationResult result = new ValidationResult(companyInfo);
        result.setMatchType(matchType);
        result.setValidationStatus(ValidationResult.ValidationStatus.NEED_MANUAL);
        result.setCandidateKeynos(candidateKeynos);
        result.setConfidenceScore(confidenceScore);
        result.setMessage(message);
        return result;
    }
}
