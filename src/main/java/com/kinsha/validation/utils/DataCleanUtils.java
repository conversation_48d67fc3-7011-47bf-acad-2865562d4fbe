package com.kinsha.validation.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.regex.Pattern;

/**
 * 数据清洗工具类
 * 提供数据预处理、清洗、验证功能
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class DataCleanUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(DataCleanUtils.class);
    
    // 弱智数字模式
    private static final Pattern WEAK_NUMBER_PATTERN = Pattern.compile("^(0+|1+0+|123456|123456789)$");
    
    // 纯中文模式
    private static final Pattern CHINESE_PATTERN = Pattern.compile("^[\\u4e00-\\u9fa5]+$");
    
    /**
     * 预处理组织机构代码
     * 1. 清洗空格、标点符号
     * 2. 统一大小写
     * 3. 过滤无效数据
     * 
     * @param originalCode 原始代码
     * @return 处理后的代码
     */
    public static String preprocessOrgCode(String originalCode) {
        // 1. 空值处理
        if (originalCode == null) {
            logger.debug("组织机构代码为null，返回空字符串");
            return "";
        }
        
        // 2. 清洗空格和标点符号
        String cleanedCode = cleanSpacesAndPunctuation(originalCode);
        
        // 3. 统一大小写（转为大写）
        String upperCaseCode = cleanedCode.toUpperCase();
        
        // 4. 无效数据过滤
        String validatedCode = filterInvalidData(upperCaseCode);
        
        return validatedCode;
    }
    
    /**
     * 清洗空格和标点符号
     * @param code 原始代码
     * @return 清洗后的代码
     */
    public static String cleanSpacesAndPunctuation(String code) {
        if (code == null || code.trim().isEmpty()) {
            return "";
        }
        
        // 移除空格
        String result = code.replaceAll("\\s+", "");
        
        // 移除常见标点符号（保留*用于脱敏标识）
        result = result.replaceAll("[，。、；：""''（）【】《》〈〉{}\\[\\]()\"',;:.!?]", "");
        
        // 移除连字符（但保留在组织机构代码中的-）
        // 只移除明显多余的连字符
        result = result.replaceAll("^-+|-+$", ""); // 移除开头和结尾的连字符
        
        logger.debug("清洗标点符号: {} -> {}", code, result);
        return result;
    }
    
    /**
     * 过滤无效数据
     * 根据文档方案2：长度<=6的认定为无效code，替换为空
     * 
     * @param code 清洗后的代码
     * @return 验证后的代码
     */
    public static String filterInvalidData(String code) {
        if (code == null || code.trim().isEmpty()) {
            return "";
        }
        
        String trimmedCode = code.trim();
        
        // 1. 长度检查：<=6的认定为无效
        if (trimmedCode.length() <= 6) {
            logger.debug("代码长度过短(<=6)，认定为无效: {}", trimmedCode);
            return "";
        }
        
        // 2. 全零检查
        if (trimmedCode.matches("^0+$")) {
            logger.debug("代码为全零，认定为无效: {}", trimmedCode);
            return "";
        }
        
        // 3. 弱智数字检查
        String codeWithoutMask = trimmedCode.replaceAll("\\*", ""); // 临时移除*进行检查
        if (WEAK_NUMBER_PATTERN.matcher(codeWithoutMask).matches()) {
            logger.debug("代码为弱智数字，认定为无效: {}", trimmedCode);
            return "";
        }
        
        // 4. 纯中文检查
        if (CHINESE_PATTERN.matcher(trimmedCode).matches()) {
            logger.debug("代码为纯中文，认定为无效: {}", trimmedCode);
            return "";
        }
        
        // 5. 特殊无效值检查
        if ("不提供".equals(trimmedCode) || "无".equals(trimmedCode) || "NULL".equals(trimmedCode)) {
            logger.debug("代码为特殊无效值，认定为无效: {}", trimmedCode);
            return "";
        }
        
        logger.debug("代码验证通过: {}", trimmedCode);
        return trimmedCode;
    }
    
    /**
     * 检查输入数据有效性
     * @param companyName 公司名称
     * @param orgCode 组织机构代码
     * @return 是否有效
     */
    public static boolean isValidInput(String companyName, String orgCode) {
        return companyName != null &&
               !companyName.trim().isEmpty() &&
               orgCode != null &&
               !orgCode.trim().isEmpty();
    }
    
    /**
     * 去除脱敏字符（*）
     * @param code 原始代码
     * @return 去除脱敏字符后的代码
     */
    public static String removeMaskingChars(String code) {
        if (code == null) {
            return "";
        }
        return code.replaceAll("\\*", "");
    }
}
