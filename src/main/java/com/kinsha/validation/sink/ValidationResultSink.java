package com.kinsha.validation.sink;

import com.kinsha.validation.model.ValidationResult;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 校验结果输出Sink
 * 负责将处理结果写入目标存储
 * TODO: 当前为控制台输出，后期可扩展为数据库、文件或消息队列输出
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ValidationResultSink implements SinkFunction<ValidationResult> {
    
    private static final Logger logger = LoggerFactory.getLogger(ValidationResultSink.class);
    private static final long serialVersionUID = 1L;
    
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    @Override
    public void invoke(ValidationResult result, Context context) throws Exception {
        // TODO: 当前为控制台输出，后期需要替换为真实的存储系统
        outputToConsole(result);
        
        // TODO: 可以在这里添加其他输出方式
        // outputToDatabase(result);
        // outputToFile(result);
        // outputToKafka(result);
    }
    
    /**
     * 输出到控制台
     * TODO: 测试用途，生产环境需要替换
     * @param result 校验结果
     */
    private void outputToConsole(ValidationResult result) {
        String timestamp = DATE_FORMAT.format(new Date(result.getProcessTimestamp()));
        
        System.out.println("=== 校验结果 ===");
        System.out.println("处理时间: " + timestamp);
        System.out.println("记录ID: " + result.getOriginalCompanyInfo().getRecordId());
        System.out.println("公司名称: " + result.getOriginalCompanyInfo().getCompanyName());
        System.out.println("原始组织机构代码: " + result.getOriginalCompanyInfo().getOriginalOrgCode());
        System.out.println("校验状态: " + result.getValidationStatus());
        System.out.println("匹配类型: " + result.getMatchType());
        System.out.println("置信度: " + String.format("%.2f", result.getConfidenceScore()));
        
        if (result.getMatchedKeyno() != null) {
            System.out.println("匹配的keyno: " + result.getMatchedKeyno());
        }
        
        if (result.getMatchedFourCode() != null) {
            System.out.println("匹配的四码信息:");
            System.out.println("  - 统一信用代码: " + result.getMatchedFourCode().getCreditCode());
            System.out.println("  - 组织机构代码: " + result.getMatchedFourCode().getOrgCode());
            System.out.println("  - 工商注册号: " + result.getMatchedFourCode().getBusinessRegNo());
            System.out.println("  - 纳税人识别号: " + result.getMatchedFourCode().getTaxNo());
        }
        
        if (result.getCandidateKeynos() != null && !result.getCandidateKeynos().isEmpty()) {
            System.out.println("候选keyno列表: " + result.getCandidateKeynos());
        }
        
        System.out.println("处理消息: " + result.getMessage());
        System.out.println("================");
        System.out.println();
        
        // 记录到日志
        logger.info("校验结果输出: 公司={}, 状态={}, 匹配类型={}, keyno={}", 
                   result.getOriginalCompanyInfo().getCompanyName(),
                   result.getValidationStatus(),
                   result.getMatchType(),
                   result.getMatchedKeyno());
    }
    
    /**
     * 输出到数据库
     * TODO: 后期实现数据库输出功能
     * @param result 校验结果
     */
    private void outputToDatabase(ValidationResult result) {
        // TODO: 实现数据库输出逻辑
        // 1. 建立数据库连接
        // 2. 构造SQL语句
        // 3. 执行插入操作
        // 4. 处理异常情况
        logger.debug("TODO: 输出到数据库功能待实现");
    }
    
    /**
     * 输出到文件
     * TODO: 后期实现文件输出功能
     * @param result 校验结果
     */
    private void outputToFile(ValidationResult result) {
        // TODO: 实现文件输出逻辑
        // 1. 格式化输出内容（JSON、CSV等）
        // 2. 写入文件
        // 3. 处理文件滚动和压缩
        // 4. 处理异常情况
        logger.debug("TODO: 输出到文件功能待实现");
    }
    
    /**
     * 输出到Kafka
     * TODO: 后期实现Kafka输出功能
     * @param result 校验结果
     */
    private void outputToKafka(ValidationResult result) {
        // TODO: 实现Kafka输出逻辑
        // 1. 序列化结果对象
        // 2. 发送到指定Topic
        // 3. 处理发送失败重试
        // 4. 处理异常情况
        logger.debug("TODO: 输出到Kafka功能待实现");
    }
}
