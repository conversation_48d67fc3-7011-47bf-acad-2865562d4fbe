package com.kinsha.validation.model;

import java.io.Serializable;

/**
 * 公司信息数据模型
 * 用于存储输入的公司名称和组织机构代码信息
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class CompanyInfo implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 公司名称 */
    private String companyName;
    
    /** 原始组织机构代码 */
    private String originalOrgCode;
    
    /** 数据来源标识 */
    private String dataSource;
    
    /** 记录ID */
    private String recordId;
    
    public CompanyInfo() {
    }
    
    public CompanyInfo(String companyName, String originalOrgCode) {
        this.companyName = companyName;
        this.originalOrgCode = originalOrgCode;
    }
    
    public CompanyInfo(String companyName, String originalOrgCode, String dataSource, String recordId) {
        this.companyName = companyName;
        this.originalOrgCode = originalOrgCode;
        this.dataSource = dataSource;
        this.recordId = recordId;
    }
    
    // Getters and Setters
    public String getCompanyName() {
        return companyName;
    }
    
    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }
    
    public String getOriginalOrgCode() {
        return originalOrgCode;
    }
    
    public void setOriginalOrgCode(String originalOrgCode) {
        this.originalOrgCode = originalOrgCode;
    }
    
    public String getDataSource() {
        return dataSource;
    }
    
    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }
    
    public String getRecordId() {
        return recordId;
    }
    
    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }
    
    @Override
    public String toString() {
        return "CompanyInfo{" +
                "companyName='" + companyName + '\'' +
                ", originalOrgCode='" + originalOrgCode + '\'' +
                ", dataSource='" + dataSource + '\'' +
                ", recordId='" + recordId + '\'' +
                '}';
    }
}
