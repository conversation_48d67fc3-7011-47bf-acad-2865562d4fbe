package com.kinsha.validation.model;

import java.io.Serializable;
import java.util.List;

/**
 * 校验结果数据模型
 * 用于存储公司与组织机构代码校验的结果
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ValidationResult implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 原始公司信息 */
    private CompanyInfo originalCompanyInfo;
    
    /** 匹配到的keyno */
    private String matchedKeyno;
    
    /** 匹配类型：EXACT(精确匹配), SIMILAR(相似匹配), NOT_FOUND(未找到), MULTIPLE(多个匹配) */
    private MatchType matchType;
    
    /** 匹配的四码信息 */
    private FourCodeInfo matchedFourCode;
    
    /** 置信度分数 */
    private double confidenceScore;
    
    /** 校验状态：SUCCESS(成功), FAILED(失败), NEED_MANUAL(需要人工处理) */
    private ValidationStatus validationStatus;
    
    /** 错误信息或备注 */
    private String message;
    
    /** 候选keyno列表（当有多个匹配时） */
    private List<String> candidateKeynos;
    
    /** 处理时间戳 */
    private long processTimestamp;
    
    public ValidationResult() {
        this.processTimestamp = System.currentTimeMillis();
    }
    
    public ValidationResult(CompanyInfo originalCompanyInfo) {
        this.originalCompanyInfo = originalCompanyInfo;
        this.processTimestamp = System.currentTimeMillis();
    }
    
    // 枚举定义
    public enum MatchType {
        EXACT,      // 精确匹配
        SIMILAR,    // 相似匹配
        NOT_FOUND,  // 未找到
        MULTIPLE    // 多个匹配
    }
    
    public enum ValidationStatus {
        SUCCESS,        // 校验成功
        FAILED,         // 校验失败
        NEED_MANUAL     // 需要人工处理
    }
    
    // Getters and Setters
    public CompanyInfo getOriginalCompanyInfo() {
        return originalCompanyInfo;
    }
    
    public void setOriginalCompanyInfo(CompanyInfo originalCompanyInfo) {
        this.originalCompanyInfo = originalCompanyInfo;
    }
    
    public String getMatchedKeyno() {
        return matchedKeyno;
    }
    
    public void setMatchedKeyno(String matchedKeyno) {
        this.matchedKeyno = matchedKeyno;
    }
    
    public MatchType getMatchType() {
        return matchType;
    }
    
    public void setMatchType(MatchType matchType) {
        this.matchType = matchType;
    }
    
    public FourCodeInfo getMatchedFourCode() {
        return matchedFourCode;
    }
    
    public void setMatchedFourCode(FourCodeInfo matchedFourCode) {
        this.matchedFourCode = matchedFourCode;
    }
    
    public double getConfidenceScore() {
        return confidenceScore;
    }
    
    public void setConfidenceScore(double confidenceScore) {
        this.confidenceScore = confidenceScore;
    }
    
    public ValidationStatus getValidationStatus() {
        return validationStatus;
    }
    
    public void setValidationStatus(ValidationStatus validationStatus) {
        this.validationStatus = validationStatus;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public List<String> getCandidateKeynos() {
        return candidateKeynos;
    }
    
    public void setCandidateKeynos(List<String> candidateKeynos) {
        this.candidateKeynos = candidateKeynos;
    }
    
    public long getProcessTimestamp() {
        return processTimestamp;
    }
    
    public void setProcessTimestamp(long processTimestamp) {
        this.processTimestamp = processTimestamp;
    }
    
    @Override
    public String toString() {
        return "ValidationResult{" +
                "originalCompanyInfo=" + originalCompanyInfo +
                ", matchedKeyno='" + matchedKeyno + '\'' +
                ", matchType=" + matchType +
                ", matchedFourCode=" + matchedFourCode +
                ", confidenceScore=" + confidenceScore +
                ", validationStatus=" + validationStatus +
                ", message='" + message + '\'' +
                ", candidateKeynos=" + candidateKeynos +
                ", processTimestamp=" + processTimestamp +
                '}';
    }
}
