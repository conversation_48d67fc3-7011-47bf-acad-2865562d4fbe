package com.kinsha.validation.model;

import java.io.Serializable;

/**
 * 四码信息数据模型
 * 包含统一信用代码、组织机构代码、工商注册号、纳税人识别号
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class FourCodeInfo implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 统一信用代码 */
    private String creditCode;
    
    /** 组织机构代码 */
    private String orgCode;
    
    /** 工商注册号 */
    private String businessRegNo;
    
    /** 纳税人识别号 */
    private String taxNo;
    
    /** 关联的keyno */
    private String keyno;
    
    public FourCodeInfo() {
    }
    
    public FourCodeInfo(String creditCode, String orgCode, String businessRegNo, String taxNo, String keyno) {
        this.creditCode = creditCode;
        this.orgCode = orgCode;
        this.businessRegNo = businessRegNo;
        this.taxNo = taxNo;
        this.keyno = keyno;
    }
    
    // Getters and Setters
    public String getCreditCode() {
        return creditCode;
    }
    
    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }
    
    public String getOrgCode() {
        return orgCode;
    }
    
    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }
    
    public String getBusinessRegNo() {
        return businessRegNo;
    }
    
    public void setBusinessRegNo(String businessRegNo) {
        this.businessRegNo = businessRegNo;
    }
    
    public String getTaxNo() {
        return taxNo;
    }
    
    public void setTaxNo(String taxNo) {
        this.taxNo = taxNo;
    }
    
    public String getKeyno() {
        return keyno;
    }
    
    public void setKeyno(String keyno) {
        this.keyno = keyno;
    }
    
    /**
     * 检查是否有任何一个四码与给定的代码匹配
     * @param code 待匹配的代码
     * @return 是否匹配
     */
    public boolean hasMatchingCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return false;
        }
        
        String cleanCode = code.trim().toUpperCase();
        
        return (creditCode != null && creditCode.trim().toUpperCase().equals(cleanCode)) ||
               (orgCode != null && orgCode.trim().toUpperCase().equals(cleanCode)) ||
               (businessRegNo != null && businessRegNo.trim().toUpperCase().equals(cleanCode)) ||
               (taxNo != null && taxNo.trim().toUpperCase().equals(cleanCode));
    }
    
    /**
     * 检查是否有任何一个四码与给定的代码相似匹配（去除脱敏字符后）
     * @param code 待匹配的代码
     * @return 是否相似匹配
     */
    public boolean hasSimilarCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return false;
        }
        
        String cleanInputCode = removeMaskingChars(code.trim().toUpperCase());
        
        return isSimilarMatch(cleanInputCode, creditCode) ||
               isSimilarMatch(cleanInputCode, orgCode) ||
               isSimilarMatch(cleanInputCode, businessRegNo) ||
               isSimilarMatch(cleanInputCode, taxNo);
    }
    
    /**
     * 去除脱敏字符（*）
     * @param code 原始代码
     * @return 去除脱敏字符后的代码
     */
    private String removeMaskingChars(String code) {
        if (code == null) {
            return "";
        }
        return code.replaceAll("\\*", "");
    }
    
    /**
     * 判断两个代码是否相似匹配
     * 规则：去掉脱敏字符后，其他字符要顺序匹配
     * @param inputCode 输入代码（已去除脱敏字符）
     * @param targetCode 目标代码
     * @return 是否相似匹配
     */
    private boolean isSimilarMatch(String inputCode, String targetCode) {
        if (inputCode == null || targetCode == null) {
            return false;
        }
        
        String cleanTargetCode = targetCode.trim().toUpperCase();
        
        // 如果输入代码为空，不匹配
        if (inputCode.isEmpty()) {
            return false;
        }
        
        // 检查输入代码的字符是否能在目标代码中按顺序找到
        int targetIndex = 0;
        for (int i = 0; i < inputCode.length(); i++) {
            char inputChar = inputCode.charAt(i);
            boolean found = false;
            
            // 在目标代码中从当前位置开始查找
            for (int j = targetIndex; j < cleanTargetCode.length(); j++) {
                if (cleanTargetCode.charAt(j) == inputChar) {
                    targetIndex = j + 1;
                    found = true;
                    break;
                }
            }
            
            // 如果某个字符没找到，则不匹配
            if (!found) {
                return false;
            }
        }
        
        return true;
    }
    
    @Override
    public String toString() {
        return "FourCodeInfo{" +
                "creditCode='" + creditCode + '\'' +
                ", orgCode='" + orgCode + '\'' +
                ", businessRegNo='" + businessRegNo + '\'' +
                ", taxNo='" + taxNo + '\'' +
                ", keyno='" + keyno + '\'' +
                '}';
    }
}
