package com.kinsha.validation.model;

import java.io.Serializable;

/**
 * 四码信息数据模型
 * 包含统一信用代码、组织机构代码、工商注册号、纳税人识别号
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class FourCodeInfo implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 统一信用代码 */
    private String creditCode;
    
    /** 组织机构代码 */
    private String orgCode;
    
    /** 工商注册号 */
    private String businessRegNo;
    
    /** 纳税人识别号 */
    private String taxNo;
    
    /** 关联的keyno */
    private String keyno;
    
    public FourCodeInfo() {
    }
    
    public FourCodeInfo(String creditCode, String orgCode, String businessRegNo, String taxNo, String keyno) {
        this.creditCode = creditCode;
        this.orgCode = orgCode;
        this.businessRegNo = businessRegNo;
        this.taxNo = taxNo;
        this.keyno = keyno;
    }
    
    // Getters and Setters
    public String getCreditCode() {
        return creditCode;
    }
    
    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }
    
    public String getOrgCode() {
        return orgCode;
    }
    
    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }
    
    public String getBusinessRegNo() {
        return businessRegNo;
    }
    
    public void setBusinessRegNo(String businessRegNo) {
        this.businessRegNo = businessRegNo;
    }
    
    public String getTaxNo() {
        return taxNo;
    }
    
    public void setTaxNo(String taxNo) {
        this.taxNo = taxNo;
    }
    
    public String getKeyno() {
        return keyno;
    }
    
    public void setKeyno(String keyno) {
        this.keyno = keyno;
    }
    
    /**
     * 检查是否有任何一个四码与给定的代码匹配
     * @param code 待匹配的代码
     * @return 是否匹配
     */
    public boolean hasMatchingCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return false;
        }
        
        String cleanCode = code.trim().toUpperCase();
        
        return (creditCode != null && creditCode.trim().toUpperCase().equals(cleanCode)) ||
               (orgCode != null && orgCode.trim().toUpperCase().equals(cleanCode)) ||
               (businessRegNo != null && businessRegNo.trim().toUpperCase().equals(cleanCode)) ||
               (taxNo != null && taxNo.trim().toUpperCase().equals(cleanCode));
    }
    
    /**
     * 检查是否有任何一个四码与给定的代码相似匹配（去除脱敏字符后）
     * @param code 待匹配的代码
     * @return 是否相似匹配
     */
    public boolean hasSimilarCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return false;
        }

        return com.kinsha.validation.utils.SimilarMatchUtils.isSimilarMatch(code, creditCode) ||
               com.kinsha.validation.utils.SimilarMatchUtils.isSimilarMatch(code, orgCode) ||
               com.kinsha.validation.utils.SimilarMatchUtils.isSimilarMatch(code, businessRegNo) ||
               com.kinsha.validation.utils.SimilarMatchUtils.isSimilarMatch(code, taxNo);
    }
    
    @Override
    public String toString() {
        return "FourCodeInfo{" +
                "creditCode='" + creditCode + '\'' +
                ", orgCode='" + orgCode + '\'' +
                ", businessRegNo='" + businessRegNo + '\'' +
                ", taxNo='" + taxNo + '\'' +
                ", keyno='" + keyno + '\'' +
                '}';
    }
}
