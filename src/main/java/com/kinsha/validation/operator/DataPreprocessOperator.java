package com.kinsha.validation.operator;

import com.kinsha.validation.model.CompanyInfo;
import com.kinsha.validation.utils.DataCleanUtils;
import org.apache.flink.api.common.functions.MapFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 数据预处理算子
 * 负责清洗空格、标点符号，统一大小写，过滤无效数据
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class DataPreprocessOperator implements MapFunction<CompanyInfo, CompanyInfo> {
    
    private static final Logger logger = LoggerFactory.getLogger(DataPreprocessOperator.class);
    private static final long serialVersionUID = 1L;
    
    @Override
    public CompanyInfo map(CompanyInfo companyInfo) throws Exception {
        logger.debug("开始预处理数据: {}", companyInfo);
        
        // 创建新的对象避免修改原始数据
        CompanyInfo processedInfo = new CompanyInfo();
        processedInfo.setCompanyName(companyInfo.getCompanyName());
        processedInfo.setDataSource(companyInfo.getDataSource());
        processedInfo.setRecordId(companyInfo.getRecordId());
        
        // 使用DataCleanUtils预处理组织机构代码
        String processedOrgCode = DataCleanUtils.preprocessOrgCode(companyInfo.getOriginalOrgCode());
        processedInfo.setOriginalOrgCode(processedOrgCode);
        
        logger.debug("预处理完成: {} -> {}", companyInfo.getOriginalOrgCode(), processedOrgCode);
        
        return processedInfo;
    }
}
