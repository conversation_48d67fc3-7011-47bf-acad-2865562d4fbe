package com.kinsha.validation.operator;

import com.kinsha.validation.model.CompanyInfo;
import com.kinsha.validation.utils.JsonUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * JSON解析算子
 * 负责将JSON字符串转换为CompanyInfo对象
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class JsonParseOperator implements FlatMapFunction<String, CompanyInfo> {
    
    private static final Logger logger = LoggerFactory.getLogger(JsonParseOperator.class);
    private static final long serialVersionUID = 1L;
    
    @Override
    public void flatMap(String jsonStr, Collector<CompanyInfo> collector) throws Exception {
        try {
            // 使用JsonUtils解析JSON字符串
            CompanyInfo companyInfo = JsonUtils.parseJsonToCompanyInfo(jsonStr);
            if (companyInfo != null) {
                collector.collect(companyInfo);
                logger.debug("JSON解析成功: {}", companyInfo);
            } else {
                logger.warn("JSON解析失败，返回null: {}", jsonStr);
            }
        } catch (Exception e) {
            logger.error("JSON解析异常: {}", jsonStr, e);
        }
    }
}
