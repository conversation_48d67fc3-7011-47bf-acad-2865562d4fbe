package com.kinsha.validation.operator;

import com.kinsha.validation.model.CompanyInfo;
import com.kinsha.validation.model.FourCodeInfo;
import com.kinsha.validation.model.ValidationResult;
import com.kinsha.validation.service.FourCodeService;
import com.kinsha.validation.service.KeynoService;
import com.kinsha.validation.utils.DataCleanUtils;
import com.kinsha.validation.utils.ValidationResultUtils;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * 公司校验算子
 * 负责核心的公司与组织机构代码校验逻辑
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class CompanyValidationOperator extends RichMapFunction<CompanyInfo, ValidationResult> {
    
    private static final Logger logger = LoggerFactory.getLogger(CompanyValidationOperator.class);
    private static final long serialVersionUID = 1L;
    
    private transient KeynoService keynoService;
    private transient FourCodeService fourCodeService;
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        // 初始化服务
        this.keynoService = new KeynoService();
        this.fourCodeService = new FourCodeService();
        logger.info("CompanyValidationOperator 初始化完成");
    }
    
    @Override
    public ValidationResult map(CompanyInfo companyInfo) throws Exception {
        logger.info("开始校验公司信息: {}", companyInfo);
        
        try {
            // 1. 检查输入数据有效性
            if (!DataCleanUtils.isValidInput(companyInfo.getCompanyName(), companyInfo.getOriginalOrgCode())) {
                return ValidationResultUtils.createFailedResult(companyInfo, "输入数据无效：公司名称或组织机构代码为空");
            }
            
            // 2. 根据公司名称获取keyno列表
            List<String> keynos = keynoService.getKeynosByCompanyName(companyInfo.getCompanyName());
            
            if (keynos.isEmpty()) {
                return ValidationResultUtils.createFailedResult(companyInfo, "未找到公司名称对应的keyno");
            }
            
            // 3. 校验四码信息
            ValidationResult validationResult = validateFourCodes(companyInfo, keynos);
            
            logger.info("校验完成: {} -> {}", companyInfo.getCompanyName(), validationResult.getValidationStatus());
            return validationResult;
            
        } catch (Exception e) {
            logger.error("校验过程中发生异常: {}", e.getMessage(), e);
            return ValidationResultUtils.createFailedResult(companyInfo, "校验过程中发生异常: " + e.getMessage());
        }
    }
    
    /**
     * 校验四码信息
     * @param companyInfo 公司信息
     * @param keynos keyno列表
     * @return 校验结果
     */
    private ValidationResult validateFourCodes(CompanyInfo companyInfo, List<String> keynos) {
        String inputOrgCode = companyInfo.getOriginalOrgCode();
        
        List<String> exactMatches = new ArrayList<>();
        List<String> similarMatches = new ArrayList<>();
        
        // 遍历所有keyno，查找匹配的四码
        for (String keyno : keynos) {
            FourCodeInfo fourCodeInfo = fourCodeService.getFourCodeByKeyno(keyno);
            
            if (fourCodeInfo == null) {
                logger.debug("keyno {} 没有对应的四码信息", keyno);
                continue;
            }
            
            // 检查精确匹配
            if (fourCodeInfo.hasMatchingCode(inputOrgCode)) {
                exactMatches.add(keyno);
                logger.info("找到精确匹配: keyno={}, 输入代码={}", keyno, inputOrgCode);
            }
            // 检查相似匹配
            else if (fourCodeInfo.hasSimilarCode(inputOrgCode)) {
                similarMatches.add(keyno);
                logger.info("找到相似匹配: keyno={}, 输入代码={}", keyno, inputOrgCode);
            }
        }
        
        // 处理匹配结果
        return processMatchResults(companyInfo, exactMatches, similarMatches, inputOrgCode);
    }
    
    /**
     * 处理匹配结果
     * @param companyInfo 公司信息
     * @param exactMatches 精确匹配的keyno列表
     * @param similarMatches 相似匹配的keyno列表
     * @param inputOrgCode 输入的组织机构代码
     * @return 处理后的校验结果
     */
    private ValidationResult processMatchResults(CompanyInfo companyInfo,
                                               List<String> exactMatches, 
                                               List<String> similarMatches, 
                                               String inputOrgCode) {
        
        // 优先处理精确匹配
        if (!exactMatches.isEmpty()) {
            if (exactMatches.size() == 1) {
                // 单个精确匹配
                String matchedKeyno = exactMatches.get(0);
                FourCodeInfo fourCodeInfo = fourCodeService.getFourCodeByKeyno(matchedKeyno);
                
                logger.info("精确匹配成功: keyno={}", matchedKeyno);
                return ValidationResultUtils.createSuccessResult(
                    companyInfo, matchedKeyno, fourCodeInfo, 
                    ValidationResult.MatchType.EXACT, 1.0, "精确匹配成功"
                );
            } else {
                // 多个精确匹配
                logger.warn("多个精确匹配: keynos={}, 输入代码={}", exactMatches, inputOrgCode);
                ValidationResultUtils.printMultipleMatchResults(exactMatches, inputOrgCode, "精确匹配", fourCodeService);
                
                return ValidationResultUtils.createManualResult(
                    companyInfo, exactMatches, ValidationResult.MatchType.MULTIPLE, 
                    0.8, "找到多个精确匹配的keyno，需要人工处理"
                );
            }
        }
        // 处理相似匹配
        else if (!similarMatches.isEmpty()) {
            if (similarMatches.size() == 1) {
                // 单个相似匹配
                String matchedKeyno = similarMatches.get(0);
                FourCodeInfo fourCodeInfo = fourCodeService.getFourCodeByKeyno(matchedKeyno);
                
                logger.info("相似匹配成功: keyno={}", matchedKeyno);
                return ValidationResultUtils.createSuccessResult(
                    companyInfo, matchedKeyno, fourCodeInfo, 
                    ValidationResult.MatchType.SIMILAR, 0.7, "相似匹配成功"
                );
            } else {
                // 多个相似匹配
                logger.warn("多个相似匹配: keynos={}, 输入代码={}", similarMatches, inputOrgCode);
                ValidationResultUtils.printMultipleMatchResults(similarMatches, inputOrgCode, "相似匹配", fourCodeService);
                
                return ValidationResultUtils.createManualResult(
                    companyInfo, similarMatches, ValidationResult.MatchType.MULTIPLE, 
                    0.5, "找到多个相似匹配的keyno，需要转入NLP处理"
                );
            }
        }
        // 没有找到匹配
        else {
            logger.info("未找到匹配: 输入代码={}", inputOrgCode);
            return ValidationResultUtils.createFailedResult(companyInfo, "未找到匹配的四码信息");
        }
    }
}
