package com.kinsha.validation.operator;

import com.kinsha.validation.model.ValidationResult;
import com.kinsha.validation.utils.ValidationResultUtils;
import org.apache.flink.api.common.functions.MapFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 校验结果输出算子
 * 负责输出校验结果到控制台
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ValidationResultOutputOperator implements MapFunction<ValidationResult, ValidationResult> {
    
    private static final Logger logger = LoggerFactory.getLogger(ValidationResultOutputOperator.class);
    private static final long serialVersionUID = 1L;
    
    @Override
    public ValidationResult map(ValidationResult result) throws Exception {
        // 使用ValidationResultUtils输出校验结果
        ValidationResultUtils.outputValidationResult(result);
        return result;
    }
}
