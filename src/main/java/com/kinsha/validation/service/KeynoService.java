package com.kinsha.validation.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * Keyno查询服务
 * 根据公司名称获取对应的keyno列表
 * TODO: 当前为测试数据，后期需要替换为真实的数据库查询或API调用
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class KeynoService {
    
    private static final Logger logger = LoggerFactory.getLogger(KeynoService.class);
    
    // TODO: 测试数据 - 公司名称到keyno的映射关系，后期需要替换为数据库查询
    private static final Map<String, List<String>> COMPANY_KEYNO_MAPPING = new HashMap<>();
    
    static {
        initializeTestData();
    }
    
    /**
     * 根据公司名称获取keyno列表
     * @param companyName 公司名称
     * @return keyno列表，如果没有找到返回空列表
     */
    public List<String> getKeynosByCompanyName(String companyName) {
        if (companyName == null || companyName.trim().isEmpty()) {
            logger.debug("公司名称为空，返回空列表");
            return new ArrayList<>();
        }
        
        String normalizedName = normalizeCompanyName(companyName);
        List<String> keynos = COMPANY_KEYNO_MAPPING.get(normalizedName);
        
        if (keynos == null) {
            // 尝试模糊匹配（去掉地区信息）
            keynos = fuzzyMatchCompanyName(normalizedName);
        }
        
        if (keynos == null) {
            keynos = new ArrayList<>();
        }
        
        logger.debug("公司名称: {} -> keynos: {}", companyName, keynos);
        return new ArrayList<>(keynos); // 返回副本避免外部修改
    }
    
    /**
     * 标准化公司名称
     * @param companyName 原始公司名称
     * @return 标准化后的公司名称
     */
    private String normalizeCompanyName(String companyName) {
        if (companyName == null) {
            return "";
        }
        
        return companyName.trim()
                .replaceAll("\\s+", "") // 移除空格
                .replaceAll("[（）()【】\\[\\]]", ""); // 移除括号
    }
    
    /**
     * 模糊匹配公司名称
     * 处理名称中缺少地区信息的情况，如"河南省"、"市"等
     * @param normalizedName 标准化后的公司名称
     * @return 匹配到的keyno列表
     */
    private List<String> fuzzyMatchCompanyName(String normalizedName) {
        logger.debug("开始模糊匹配公司名称: {}", normalizedName);
        
        // 尝试在现有映射中查找包含该名称的条目
        for (Map.Entry<String, List<String>> entry : COMPANY_KEYNO_MAPPING.entrySet()) {
            String mappedName = entry.getKey();
            
            // 检查是否为子串匹配（去掉地区信息后的匹配）
            if (mappedName.contains(normalizedName) || normalizedName.contains(mappedName)) {
                logger.debug("模糊匹配成功: {} -> {}", normalizedName, entry.getValue());
                return entry.getValue();
            }
        }
        
        logger.debug("模糊匹配失败: {}", normalizedName);
        return null;
    }
    
    /**
     * 初始化测试数据
     * TODO: 这些是测试数据，后期需要替换为真实的数据库查询
     */
    private static void initializeTestData() {
        // 测试案例1：单个keyno匹配
        COMPANY_KEYNO_MAPPING.put("阿拉善盟旭阳煤业有限公司", 
            Arrays.asList("18a4d6c0d08d6265de4d821bfaa02ff5"));
        
        // 测试案例2：单个keyno匹配
        COMPANY_KEYNO_MAPPING.put("安徽力天建设有限公司", 
            Arrays.asList("0f5c8b0b4044915bde57365c320b0215"));
        
        // 测试案例3：单个keyno匹配
        COMPANY_KEYNO_MAPPING.put("安徽明珠建设工程有限公司", 
            Arrays.asList("154b585d3bc23dd66305ec7c97f5d191"));
        
        // 测试案例4：单个keyno匹配
        COMPANY_KEYNO_MAPPING.put("北京科技发展有限公司", 
            Arrays.asList("a1b2c3d4e5f6789012345678901234ab"));
        
        // 测试案例5：多个keyno匹配的情况
        COMPANY_KEYNO_MAPPING.put("上海贸易有限公司", 
            Arrays.asList(
                "keyno001_shanghai_trade_1",
                "keyno002_shanghai_trade_2",
                "keyno003_shanghai_trade_3"
            ));
        
        // 测试案例6：用于模糊匹配的完整名称
        COMPANY_KEYNO_MAPPING.put("河南省明珠建设工程有限公司", 
            Arrays.asList("154b585d3bc23dd66305ec7c97f5d191"));
        
        // 测试案例7：另一个多keyno的案例
        COMPANY_KEYNO_MAPPING.put("阿拉善盟旭阳煤业有限公司", 
            Arrays.asList(
                "18a4d6c0d08d6265de4d821bfaa02ff5",
                "83a89a73952e14aa5c2700efefeb537d"
            ));
        
        // 测试案例8：特殊字符测试
        COMPANY_KEYNO_MAPPING.put("特殊字符测试公司", 
            Arrays.asList("special_char_test_keyno_001"));
        
        // 测试案例9：大小写测试
        COMPANY_KEYNO_MAPPING.put("大小写测试公司", 
            Arrays.asList("case_test_keyno_001"));
        
        // 测试案例10：边界测试
        COMPANY_KEYNO_MAPPING.put("边界测试公司", 
            Arrays.asList("boundary_test_keyno_001"));
    }
}
