package com.kinsha.validation.service;

import com.kinsha.validation.model.FourCodeInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * 四码查询服务
 * 根据keyno获取对应的四码信息（统一信用代码、组织机构代码、工商注册号、纳税人识别号）
 * TODO: 当前为测试数据，后期需要替换为真实的数据库查询
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class FourCodeService {
    
    private static final Logger logger = LoggerFactory.getLogger(FourCodeService.class);
    
    // TODO: 测试数据 - keyno到四码信息的映射关系，后期需要替换为数据库查询
    private static final Map<String, FourCodeInfo> KEYNO_FOURCODE_MAPPING = new HashMap<>();
    
    static {
        initializeTestData();
    }
    
    /**
     * 根据keyno获取四码信息
     * @param keyno 企业唯一标识
     * @return 四码信息，如果没有找到返回null
     */
    public FourCodeInfo getFourCodeByKeyno(String keyno) {
        if (keyno == null || keyno.trim().isEmpty()) {
            logger.debug("keyno为空，返回null");
            return null;
        }
        
        FourCodeInfo fourCodeInfo = KEYNO_FOURCODE_MAPPING.get(keyno.trim());
        
        if (fourCodeInfo == null) {
            logger.debug("未找到keyno对应的四码信息: {}", keyno);
        } else {
            logger.debug("找到keyno对应的四码信息: {} -> {}", keyno, fourCodeInfo);
        }
        
        return fourCodeInfo;
    }
    
    /**
     * 初始化测试数据
     * TODO: 这些是测试数据，后期需要替换为真实的数据库查询
     */
    private static void initializeTestData() {
        // 测试案例1：阿拉善盟旭阳煤业有限公司 - 第一个keyno
        KEYNO_FOURCODE_MAPPING.put("18a4d6c0d08d6265de4d821bfaa02ff5",
            new FourCodeInfo(
                "91152923578859022X",  // 统一信用代码
                "57885902-2",          // 组织机构代码
                "152923000001234",     // 工商注册号
                "91152923578859022X",  // 纳税人识别号
                "18a4d6c0d08d6265de4d821bfaa02ff5"
            ));
        
        // 测试案例2：阿拉善盟旭阳煤业有限公司 - 第二个keyno（用于测试多keyno情况）
        KEYNO_FOURCODE_MAPPING.put("83a89a73952e14aa5c2700efefeb537d",
            new FourCodeInfo(
                "91150402089588305X",  // 统一信用代码
                "08958830-5",          // 组织机构代码
                "150402000005678",     // 工商注册号
                "91150402089588305X",  // 纳税人识别号
                "83a89a73952e14aa5c2700efefeb537d"
            ));
        
        // 测试案例3：安徽力天建设有限公司
        KEYNO_FOURCODE_MAPPING.put("0f5c8b0b4044915bde57365c320b0215",
            new FourCodeInfo(
                "91340100754884324Y",  // 统一信用代码
                "75488432-4",          // 组织机构代码
                "340100000012345",     // 工商注册号
                "91340100754884324Y",  // 纳税人识别号
                "0f5c8b0b4044915bde57365c320b0215"
            ));
        
        // 测试案例4：安徽明珠建设工程有限公司
        KEYNO_FOURCODE_MAPPING.put("154b585d3bc23dd66305ec7c97f5d191",
            new FourCodeInfo(
                "91341200799810185J",  // 统一信用代码
                "79981018-5",          // 组织机构代码
                "341200000098765",     // 工商注册号
                "91341200799810185J",  // 纳税人识别号
                "154b585d3bc23dd66305ec7c97f5d191"
            ));
        
        // 测试案例5：北京科技发展有限公司
        KEYNO_FOURCODE_MAPPING.put("a1b2c3d4e5f6789012345678901234ab",
            new FourCodeInfo(
                "91110000320584000923", // 统一信用代码
                "320584000120923",      // 组织机构代码（用于测试相似匹配 32058400****0923）
                "110000000011111",      // 工商注册号
                "91110000320584000923", // 纳税人识别号
                "a1b2c3d4e5f6789012345678901234ab"
            ));
        
        // 测试案例6：上海贸易有限公司 - 第一个keyno
        KEYNO_FOURCODE_MAPPING.put("keyno001_shanghai_trade_1",
            new FourCodeInfo(
                "91310000123456789X",  // 统一信用代码
                "12345678-9",          // 组织机构代码
                "310000000001111",     // 工商注册号
                "91310000123456789X",  // 纳税人识别号
                "keyno001_shanghai_trade_1"
            ));
        
        // 测试案例7：上海贸易有限公司 - 第二个keyno
        KEYNO_FOURCODE_MAPPING.put("keyno002_shanghai_trade_2",
            new FourCodeInfo(
                "91310000987654321Y",  // 统一信用代码
                "98765432-1",          // 组织机构代码
                "310000000002222",     // 工商注册号
                "91310000987654321Y",  // 纳税人识别号
                "keyno002_shanghai_trade_2"
            ));
        
        // 测试案例8：上海贸易有限公司 - 第三个keyno
        KEYNO_FOURCODE_MAPPING.put("keyno003_shanghai_trade_3",
            new FourCodeInfo(
                "91310000555666777Z",  // 统一信用代码
                "55566677-7",          // 组织机构代码
                "310000000003333",     // 工商注册号
                "91310000555666777Z",  // 纳税人识别号
                "keyno003_shanghai_trade_3"
            ));
        
        // 测试案例9：特殊字符测试公司
        KEYNO_FOURCODE_MAPPING.put("special_char_test_keyno_001",
            new FourCodeInfo(
                "91310000123456789X",  // 统一信用代码
                "12345678-9",          // 组织机构代码
                "310000000004444",     // 工商注册号
                "91310000123456789X",  // 纳税人识别号
                "special_char_test_keyno_001"
            ));
        
        // 测试案例10：大小写测试公司
        KEYNO_FOURCODE_MAPPING.put("case_test_keyno_001",
            new FourCodeInfo(
                "9131000012345678X",   // 统一信用代码（小写x用于测试大小写处理）
                "12345678-x",          // 组织机构代码
                "310000000005555",     // 工商注册号
                "9131000012345678X",   // 纳税人识别号
                "case_test_keyno_001"
            ));
        
        // 测试案例11：边界测试公司
        KEYNO_FOURCODE_MAPPING.put("boundary_test_keyno_001",
            new FourCodeInfo(
                "91310000123456",      // 统一信用代码（较短）
                "123456",              // 组织机构代码（较短）
                "310000000006666",     // 工商注册号
                "91310000123456",      // 纳税人识别号
                "boundary_test_keyno_001"
            ));
    }
}
