package com.kinsha.validation.function;

import com.kinsha.validation.model.CompanyInfo;
import com.kinsha.validation.model.FourCodeInfo;
import com.kinsha.validation.model.ValidationResult;
import com.kinsha.validation.service.FourCodeService;
import com.kinsha.validation.service.KeynoService;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * 公司与组织机构代码校验函数
 * 核心业务逻辑：根据公司名称获取keyno，然后校验四码信息
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class CompanyValidationFunction extends RichMapFunction<CompanyInfo, ValidationResult> {
    
    private static final Logger logger = LoggerFactory.getLogger(CompanyValidationFunction.class);
    private static final long serialVersionUID = 1L;
    
    private transient KeynoService keynoService;
    private transient FourCodeService fourCodeService;
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        // 初始化服务
        this.keynoService = new KeynoService();
        this.fourCodeService = new FourCodeService();
        logger.info("CompanyValidationFunction 初始化完成");
    }
    
    @Override
    public ValidationResult map(CompanyInfo companyInfo) throws Exception {
        logger.info("开始校验公司信息: {}", companyInfo);
        
        ValidationResult result = new ValidationResult(companyInfo);
        
        try {
            // 1. 检查输入数据有效性
            if (!isValidInput(companyInfo)) {
                result.setValidationStatus(ValidationResult.ValidationStatus.FAILED);
                result.setMatchType(ValidationResult.MatchType.NOT_FOUND);
                result.setMessage("输入数据无效：公司名称或组织机构代码为空");
                return result;
            }
            
            // 2. 根据公司名称获取keyno列表
            List<String> keynos = keynoService.getKeynosByCompanyName(companyInfo.getCompanyName());
            
            if (keynos.isEmpty()) {
                result.setValidationStatus(ValidationResult.ValidationStatus.FAILED);
                result.setMatchType(ValidationResult.MatchType.NOT_FOUND);
                result.setMessage("未找到公司名称对应的keyno");
                return result;
            }
            
            // 3. 校验四码信息
            ValidationResult validationResult = validateFourCodes(companyInfo, keynos);
            
            logger.info("校验完成: {} -> {}", companyInfo.getCompanyName(), validationResult.getValidationStatus());
            return validationResult;
            
        } catch (Exception e) {
            logger.error("校验过程中发生异常: {}", e.getMessage(), e);
            result.setValidationStatus(ValidationResult.ValidationStatus.FAILED);
            result.setMatchType(ValidationResult.MatchType.NOT_FOUND);
            result.setMessage("校验过程中发生异常: " + e.getMessage());
            return result;
        }
    }
    
    /**
     * 检查输入数据有效性
     * @param companyInfo 公司信息
     * @return 是否有效
     */
    private boolean isValidInput(CompanyInfo companyInfo) {
        return companyInfo != null &&
               companyInfo.getCompanyName() != null &&
               !companyInfo.getCompanyName().trim().isEmpty() &&
               companyInfo.getOriginalOrgCode() != null &&
               !companyInfo.getOriginalOrgCode().trim().isEmpty();
    }
    
    /**
     * 校验四码信息
     * @param companyInfo 公司信息
     * @param keynos keyno列表
     * @return 校验结果
     */
    private ValidationResult validateFourCodes(CompanyInfo companyInfo, List<String> keynos) {
        ValidationResult result = new ValidationResult(companyInfo);
        String inputOrgCode = companyInfo.getOriginalOrgCode();
        
        List<String> exactMatches = new ArrayList<>();
        List<String> similarMatches = new ArrayList<>();
        
        // 遍历所有keyno，查找匹配的四码
        for (String keyno : keynos) {
            FourCodeInfo fourCodeInfo = fourCodeService.getFourCodeByKeyno(keyno);
            
            if (fourCodeInfo == null) {
                logger.debug("keyno {} 没有对应的四码信息", keyno);
                continue;
            }
            
            // 检查精确匹配
            if (fourCodeInfo.hasMatchingCode(inputOrgCode)) {
                exactMatches.add(keyno);
                logger.info("找到精确匹配: keyno={}, 输入代码={}", keyno, inputOrgCode);
            }
            // 检查相似匹配
            else if (fourCodeInfo.hasSimilarCode(inputOrgCode)) {
                similarMatches.add(keyno);
                logger.info("找到相似匹配: keyno={}, 输入代码={}", keyno, inputOrgCode);
            }
        }
        
        // 处理匹配结果
        return processMatchResults(result, exactMatches, similarMatches, inputOrgCode);
    }
    
    /**
     * 处理匹配结果
     * @param result 校验结果对象
     * @param exactMatches 精确匹配的keyno列表
     * @param similarMatches 相似匹配的keyno列表
     * @param inputOrgCode 输入的组织机构代码
     * @return 处理后的校验结果
     */
    private ValidationResult processMatchResults(ValidationResult result, 
                                               List<String> exactMatches, 
                                               List<String> similarMatches, 
                                               String inputOrgCode) {
        
        // 优先处理精确匹配
        if (!exactMatches.isEmpty()) {
            if (exactMatches.size() == 1) {
                // 单个精确匹配
                String matchedKeyno = exactMatches.get(0);
                FourCodeInfo fourCodeInfo = fourCodeService.getFourCodeByKeyno(matchedKeyno);
                
                result.setMatchedKeyno(matchedKeyno);
                result.setMatchedFourCode(fourCodeInfo);
                result.setMatchType(ValidationResult.MatchType.EXACT);
                result.setValidationStatus(ValidationResult.ValidationStatus.SUCCESS);
                result.setConfidenceScore(1.0);
                result.setMessage("精确匹配成功");
                
                logger.info("精确匹配成功: keyno={}", matchedKeyno);
            } else {
                // 多个精确匹配
                result.setMatchType(ValidationResult.MatchType.MULTIPLE);
                result.setValidationStatus(ValidationResult.ValidationStatus.NEED_MANUAL);
                result.setCandidateKeynos(exactMatches);
                result.setConfidenceScore(0.8);
                result.setMessage("找到多个精确匹配的keyno，需要人工处理");
                
                logger.warn("多个精确匹配: keynos={}, 输入代码={}", exactMatches, inputOrgCode);
                // TODO: 根据文档要求，多个匹配时打印结果
                printMultipleMatchResults(exactMatches, inputOrgCode, "精确匹配");
            }
        }
        // 处理相似匹配
        else if (!similarMatches.isEmpty()) {
            if (similarMatches.size() == 1) {
                // 单个相似匹配
                String matchedKeyno = similarMatches.get(0);
                FourCodeInfo fourCodeInfo = fourCodeService.getFourCodeByKeyno(matchedKeyno);
                
                result.setMatchedKeyno(matchedKeyno);
                result.setMatchedFourCode(fourCodeInfo);
                result.setMatchType(ValidationResult.MatchType.SIMILAR);
                result.setValidationStatus(ValidationResult.ValidationStatus.SUCCESS);
                result.setConfidenceScore(0.7);
                result.setMessage("相似匹配成功");
                
                logger.info("相似匹配成功: keyno={}", matchedKeyno);
            } else {
                // 多个相似匹配
                result.setMatchType(ValidationResult.MatchType.MULTIPLE);
                result.setValidationStatus(ValidationResult.ValidationStatus.NEED_MANUAL);
                result.setCandidateKeynos(similarMatches);
                result.setConfidenceScore(0.5);
                result.setMessage("找到多个相似匹配的keyno，需要转入NLP处理");
                
                logger.warn("多个相似匹配: keynos={}, 输入代码={}", similarMatches, inputOrgCode);
                // TODO: 根据文档要求，多个匹配时打印结果
                printMultipleMatchResults(similarMatches, inputOrgCode, "相似匹配");
            }
        }
        // 没有找到匹配
        else {
            result.setMatchType(ValidationResult.MatchType.NOT_FOUND);
            result.setValidationStatus(ValidationResult.ValidationStatus.FAILED);
            result.setConfidenceScore(0.0);
            result.setMessage("未找到匹配的四码信息");
            
            logger.info("未找到匹配: 输入代码={}", inputOrgCode);
        }
        
        return result;
    }
    
    /**
     * 打印多个匹配结果
     * TODO: 根据文档要求，当多个keyno都匹配时打印结果
     * @param matchedKeynos 匹配的keyno列表
     * @param inputOrgCode 输入的组织机构代码
     * @param matchType 匹配类型
     */
    private void printMultipleMatchResults(List<String> matchedKeynos, String inputOrgCode, String matchType) {
        logger.warn("=== 多个{}结果 ===", matchType);
        logger.warn("输入组织机构代码: {}", inputOrgCode);
        logger.warn("匹配到的keyno数量: {}", matchedKeynos.size());
        
        for (int i = 0; i < matchedKeynos.size(); i++) {
            String keyno = matchedKeynos.get(i);
            FourCodeInfo fourCodeInfo = fourCodeService.getFourCodeByKeyno(keyno);
            
            logger.warn("候选{}. keyno: {}", (i + 1), keyno);
            if (fourCodeInfo != null) {
                logger.warn("  - 统一信用代码: {}", fourCodeInfo.getCreditCode());
                logger.warn("  - 组织机构代码: {}", fourCodeInfo.getOrgCode());
                logger.warn("  - 工商注册号: {}", fourCodeInfo.getBusinessRegNo());
                logger.warn("  - 纳税人识别号: {}", fourCodeInfo.getTaxNo());
            }
        }
        logger.warn("=== 多个{}结果结束 ===", matchType);
    }
}
