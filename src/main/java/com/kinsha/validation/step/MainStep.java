package com.kinsha.validation.step;

import com.kinsha.validation.model.CompanyInfo;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;

/**
 * 被执行人公司与组织机构校验规则主流程
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class MainStep extends MainParentStep {
    
    private static final Logger logger = LoggerFactory.getLogger(MainStep.class);
    private StepConstructorParam stepConstructorParam;
    
    public MainStep(StepConstructorParam stepConstructorParam) throws Exception {
        this.stepConstructorParam = stepConstructorParam;
    }
    
    @Override
    protected DataStream<String> getSpiderSourceData() {
        logger.info("获取爬虫数据源 - 被执行人公司信息");
        
        // TODO: 这里使用测试数据，后期需要替换为真实的Kafka数据源
        List<CompanyInfo> testData = createTestData();
        
        // 将测试数据转换为字符串流
        return stepConstructorParam.getEnv()
                .fromCollection(testData)
                .flatMap(new FlatMapFunction<CompanyInfo, String>() {
                    @Override
                    public void flatMap(CompanyInfo companyInfo, Collector<String> collector) throws Exception {
                        // 将CompanyInfo转换为JSON字符串
                        String jsonStr = String.format(
                            "{\"companyName\":\"%s\",\"originalOrgCode\":\"%s\",\"dataSource\":\"%s\",\"recordId\":\"%s\"}",
                            companyInfo.getCompanyName(),
                            companyInfo.getOriginalOrgCode(),
                            companyInfo.getDataSource(),
                            companyInfo.getRecordId()
                        );
                        collector.collect(jsonStr);
                    }
                })
                .setParallelism(1);
    }
    
    @Override
    protected DataStream<String> getDapSourceData() {
        logger.info("获取DAP数据源 - 当前业务不需要DAP数据源");
        
        // TODO: 被执行人公司校验规则暂时不需要DAP数据源，直接返回null
        // 如果后期需要，可以在这里添加DAP数据源的获取逻辑
        return null;
    }
    
    @Override
    protected void spiderStep(DataStream<String> input) {
        logger.info("开始执行爬虫数据处理步骤");
        
        // 设置并行度
        int spiderProcessParallelism = stepConstructorParam.getDmpParams() != null ? 
            stepConstructorParam.getDmpParams().getInt("spider.process.parallelism", 1) : 1;
        stepConstructorParam.getEnv().setParallelism(spiderProcessParallelism);
        
        // 设置核心线程池大小
        int corePoolSize = stepConstructorParam.getDmpParams() != null ?
            stepConstructorParam.getDmpParams().getInt("core.pool.size", 1) : 1;
        stepConstructorParam.setCorePoolSize(corePoolSize);
        
        // 执行爬虫处理步骤
        new SpiderStep(stepConstructorParam).process(input);
    }
    
    @Override
    protected void dapStep(DataStream<String> input) {
        logger.info("开始执行DAP数据处理步骤");
        
        // 设置并行度
        stepConstructorParam.getEnv().setParallelism(1);
        
        // 执行DAP处理步骤
        new DapStep().process(input);
    }
    
    /**
     * 创建测试数据
     * TODO: 这些是测试数据，需要根据实际业务场景替换
     * @return 测试数据列表
     */
    private List<CompanyInfo> createTestData() {
        return Arrays.asList(
            // 测试案例1：精确匹配 - 组织机构代码完全一致
            new CompanyInfo("阿拉善盟旭阳煤业有限公司", "57885902-2", "spider_source", "001"),
            
            // 测试案例2：精确匹配 - 统一信用代码完全一致  
            new CompanyInfo("安徽力天建设有限公司", "75488432-4", "spider_source", "002"),
            
            // 测试案例3：相似匹配 - 统一信用代码部分脱敏
            new CompanyInfo("安徽明珠建设工程有限公司", "9134120079****185J", "spider_source", "003"),
            
            // 测试案例4：相似匹配 - 组织机构代码部分脱敏
            new CompanyInfo("北京科技发展有限公司", "32058400****0923", "spider_source", "004"),
            
            // 测试案例5：无法匹配 - 代码不存在
            new CompanyInfo("阿荣旗那吉镇三合运输车队", "L0517650-X", "spider_source", "005"),
            
            // 测试案例6：无效数据 - 代码长度过短
            new CompanyInfo("测试公司A", "458", "spider_source", "006"),
            
            // 测试案例7：无效数据 - 纯数字弱智代码
            new CompanyInfo("测试公司B", "123456", "spider_source", "007"),
            
            // 测试案例8：无效数据 - 全零代码
            new CompanyInfo("测试公司C", "0000", "spider_source", "008"),
            
            // 测试案例9：多个匹配 - 可能匹配多个keyno的情况
            new CompanyInfo("上海贸易有限公司", "91310000123456789X", "spider_source", "009"),
            
            // 测试案例10：名称不完整 - 缺少地区信息
            new CompanyInfo("明珠建设工程有限公司", "79981018-5", "spider_source", "010"),
            
            // 测试案例11：空值测试
            new CompanyInfo("空代码测试公司", "", "spider_source", "011"),
            
            // 测试案例12：null值测试
            new CompanyInfo("null代码测试公司", null, "spider_source", "012"),
            
            // 测试案例13：包含特殊字符
            new CompanyInfo("特殊字符测试公司", "91-310-000-123456789X", "spider_source", "013"),
            
            // 测试案例14：大小写混合
            new CompanyInfo("大小写测试公司", "9131000012345678x", "spider_source", "014"),
            
            // 测试案例15：长度边界测试
            new CompanyInfo("边界测试公司", "123456", "spider_source", "015")
        );
    }
}
