package com.kinsha.validation.step;

import org.apache.flink.streaming.api.datastream.DataStream;

/**
 * DAP处理步骤父类
 * 定义DAP数据处理的标准流程
 * 
 * <AUTHOR>
 * @version 1.0
 * @param <T> 数据类型
 */
public abstract class DapParentStep<T> {
    
    /**
     * 处理DAP数据的主流程
     * @param input 输入数据流
     */
    public void process(DataStream<String> input) {
        // 1. 字符串转对象
        DataStream<T> objectStream = transformDapStringToObjecFlatMap(input);
        
        // 2. 合并DAP和基础数据
        DataStream<T> mergedStream = mergeDapAndBaseFunction(objectStream);
        
        // 3. 清洗步骤
        cleanStep(mergedStream);
        
        // 4. 保存到基础数据库
        saveBaseDB(mergedStream);
    }
    
    /**
     * 将DAP字符串转换为对象
     * @param dataStream 输入字符串流
     * @return 转换后的对象流
     */
    protected abstract DataStream<T> transformDapStringToObjecFlatMap(DataStream<String> dataStream);
    
    /**
     * 合并DAP和基础数据
     * @param dataStream 数据流
     * @return 合并后的数据流
     */
    protected abstract DataStream<T> mergeDapAndBaseFunction(DataStream<T> dataStream);
    
    /**
     * 清洗步骤
     * @param dataStream 数据流
     */
    protected abstract void cleanStep(DataStream<T> dataStream);
    
    /**
     * 保存到基础数据库
     * @param dataStream 数据流
     */
    protected abstract void saveBaseDB(DataStream<T> dataStream);
}
