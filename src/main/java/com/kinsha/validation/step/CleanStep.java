package com.kinsha.validation.step;

import com.kinsha.validation.model.CompanyInfo;
import com.kinsha.validation.model.ValidationResult;
import com.kinsha.validation.operator.CompanyValidationOperator;
import com.kinsha.validation.operator.ValidationResultOutputOperator;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 被执行人公司与组织机构校验规则清洗步骤
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class CleanStep extends CleanParentStep<CompanyInfo, ValidationResult> {
    
    private static final Logger logger = LoggerFactory.getLogger(CleanStep.class);
    private int corePoolSize;

    public CleanStep(int corePoolSize) {
        this.corePoolSize = corePoolSize;
    }
    
    @Override
    protected DataStream<ValidationResult> transformBaseObjectToProdObjectFlatMap(DataStream<CompanyInfo> dataStream) {
        logger.info("步骤1: 将CompanyInfo转换为ValidationResult - 核心校验逻辑");

        return dataStream
                .map(new CompanyValidationOperator())
                .name("CompanyValidation");
    }
    
    @Override
    protected DataStream<ValidationResult> queryCourtInfoFunction(DataStream<ValidationResult> dataStream) {
        logger.info("步骤2: 查询法院信息 (当前业务不需要)");
        
        // TODO: 被执行人公司校验规则不涉及法院信息查询，直接返回
        return dataStream;
    }
    
    @Override
    protected void pushRiskMessageFunction(DataStream<List<ValidationResult>> dataStream) {
        logger.info("步骤3: 推送风险消息 (当前业务不需要)");
        
        // TODO: 被执行人公司校验规则暂时不需要推送风险消息
        // 如果后期需要，可以在这里添加风险消息推送逻辑
    }
    
    @Override
    protected void saveBusinessDataFunction(DataStream<List<ValidationResult>> dataStream) {
        logger.info("步骤4: 保存业务数据 (当前业务不需要)");
        
        // TODO: 被执行人公司校验规则暂时不需要保存业务数据
        // 如果后期需要，可以在这里添加业务数据保存逻辑
    }
    
    @Override
    protected void cleanAndSaveDetailDataFunction(DataStream<ValidationResult> dataStream) {
        logger.info("步骤5: 清洗并保存详细数据 - 输出校验结果");

        // 使用ValidationResultOutputOperator输出校验结果
        dataStream
                .map(new ValidationResultOutputOperator())
                .name("OutputValidationResult");
    }
    
    @Override
    protected void cleanAndRefreshCountDataFunction(DataStream<ValidationResult> dataStream) {
        logger.info("步骤6: 清洗并刷新统计数据 (当前业务不需要)");
        
        // TODO: 被执行人公司校验规则暂时不需要统计数据
        // 如果后期需要，可以在这里添加统计数据处理逻辑
    }
    

}
