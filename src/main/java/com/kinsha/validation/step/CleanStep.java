package com.kinsha.validation.step;

import com.kinsha.validation.model.CompanyInfo;
import com.kinsha.validation.model.FourCodeInfo;
import com.kinsha.validation.model.ValidationResult;
import com.kinsha.validation.service.FourCodeService;
import com.kinsha.validation.service.KeynoService;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * 被执行人公司与组织机构校验规则清洗步骤
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class CleanStep extends CleanParentStep<CompanyInfo, ValidationResult> {
    
    private static final Logger logger = LoggerFactory.getLogger(CleanStep.class);
    private int corePoolSize;
    
    // 业务服务
    private transient KeynoService keynoService;
    private transient FourCodeService fourCodeService;
    
    public CleanStep(int corePoolSize) {
        this.corePoolSize = corePoolSize;
        // 初始化服务
        this.keynoService = new KeynoService();
        this.fourCodeService = new FourCodeService();
    }
    
    @Override
    protected DataStream<ValidationResult> transformBaseObjectToProdObjectFlatMap(DataStream<CompanyInfo> dataStream) {
        logger.info("步骤1: 将CompanyInfo转换为ValidationResult - 核心校验逻辑");
        
        return dataStream.map(new MapFunction<CompanyInfo, ValidationResult>() {
            @Override
            public ValidationResult map(CompanyInfo companyInfo) throws Exception {
                logger.info("开始校验公司信息: {}", companyInfo);
                
                ValidationResult result = new ValidationResult(companyInfo);
                
                try {
                    // 1. 检查输入数据有效性
                    if (!isValidInput(companyInfo)) {
                        result.setValidationStatus(ValidationResult.ValidationStatus.FAILED);
                        result.setMatchType(ValidationResult.MatchType.NOT_FOUND);
                        result.setMessage("输入数据无效：公司名称或组织机构代码为空");
                        return result;
                    }
                    
                    // 2. 根据公司名称获取keyno列表
                    List<String> keynos = keynoService.getKeynosByCompanyName(companyInfo.getCompanyName());
                    
                    if (keynos.isEmpty()) {
                        result.setValidationStatus(ValidationResult.ValidationStatus.FAILED);
                        result.setMatchType(ValidationResult.MatchType.NOT_FOUND);
                        result.setMessage("未找到公司名称对应的keyno");
                        return result;
                    }
                    
                    // 3. 校验四码信息
                    ValidationResult validationResult = validateFourCodes(companyInfo, keynos);
                    
                    logger.info("校验完成: {} -> {}", companyInfo.getCompanyName(), validationResult.getValidationStatus());
                    return validationResult;
                    
                } catch (Exception e) {
                    logger.error("校验过程中发生异常: {}", e.getMessage(), e);
                    result.setValidationStatus(ValidationResult.ValidationStatus.FAILED);
                    result.setMatchType(ValidationResult.MatchType.NOT_FOUND);
                    result.setMessage("校验过程中发生异常: " + e.getMessage());
                    return result;
                }
            }
        }).name("CompanyValidation");
    }
    
    @Override
    protected DataStream<ValidationResult> queryCourtInfoFunction(DataStream<ValidationResult> dataStream) {
        logger.info("步骤2: 查询法院信息 (当前业务不需要)");
        
        // TODO: 被执行人公司校验规则不涉及法院信息查询，直接返回
        return dataStream;
    }
    
    @Override
    protected void pushRiskMessageFunction(DataStream<List<ValidationResult>> dataStream) {
        logger.info("步骤3: 推送风险消息 (当前业务不需要)");
        
        // TODO: 被执行人公司校验规则暂时不需要推送风险消息
        // 如果后期需要，可以在这里添加风险消息推送逻辑
    }
    
    @Override
    protected void saveBusinessDataFunction(DataStream<List<ValidationResult>> dataStream) {
        logger.info("步骤4: 保存业务数据 (当前业务不需要)");
        
        // TODO: 被执行人公司校验规则暂时不需要保存业务数据
        // 如果后期需要，可以在这里添加业务数据保存逻辑
    }
    
    @Override
    protected void cleanAndSaveDetailDataFunction(DataStream<ValidationResult> dataStream) {
        logger.info("步骤5: 清洗并保存详细数据 - 输出校验结果");
        
        // 输出校验结果到控制台
        dataStream.map(new MapFunction<ValidationResult, ValidationResult>() {
            @Override
            public ValidationResult map(ValidationResult result) throws Exception {
                // 输出校验结果
                outputValidationResult(result);
                return result;
            }
        }).name("OutputValidationResult");
    }
    
    @Override
    protected void cleanAndRefreshCountDataFunction(DataStream<ValidationResult> dataStream) {
        logger.info("步骤6: 清洗并刷新统计数据 (当前业务不需要)");
        
        // TODO: 被执行人公司校验规则暂时不需要统计数据
        // 如果后期需要，可以在这里添加统计数据处理逻辑
    }
    
    /**
     * 检查输入数据有效性
     * @param companyInfo 公司信息
     * @return 是否有效
     */
    private boolean isValidInput(CompanyInfo companyInfo) {
        return companyInfo != null &&
               companyInfo.getCompanyName() != null &&
               !companyInfo.getCompanyName().trim().isEmpty() &&
               companyInfo.getOriginalOrgCode() != null &&
               !companyInfo.getOriginalOrgCode().trim().isEmpty();
    }
    
    /**
     * 校验四码信息
     * @param companyInfo 公司信息
     * @param keynos keyno列表
     * @return 校验结果
     */
    private ValidationResult validateFourCodes(CompanyInfo companyInfo, List<String> keynos) {
        ValidationResult result = new ValidationResult(companyInfo);
        String inputOrgCode = companyInfo.getOriginalOrgCode();
        
        List<String> exactMatches = new ArrayList<>();
        List<String> similarMatches = new ArrayList<>();
        
        // 遍历所有keyno，查找匹配的四码
        for (String keyno : keynos) {
            FourCodeInfo fourCodeInfo = fourCodeService.getFourCodeByKeyno(keyno);
            
            if (fourCodeInfo == null) {
                logger.debug("keyno {} 没有对应的四码信息", keyno);
                continue;
            }
            
            // 检查精确匹配
            if (fourCodeInfo.hasMatchingCode(inputOrgCode)) {
                exactMatches.add(keyno);
                logger.info("找到精确匹配: keyno={}, 输入代码={}", keyno, inputOrgCode);
            }
            // 检查相似匹配
            else if (fourCodeInfo.hasSimilarCode(inputOrgCode)) {
                similarMatches.add(keyno);
                logger.info("找到相似匹配: keyno={}, 输入代码={}", keyno, inputOrgCode);
            }
        }
        
        // 处理匹配结果
        return processMatchResults(result, exactMatches, similarMatches, inputOrgCode);
    }
    
    /**
     * 处理匹配结果
     * @param result 校验结果对象
     * @param exactMatches 精确匹配的keyno列表
     * @param similarMatches 相似匹配的keyno列表
     * @param inputOrgCode 输入的组织机构代码
     * @return 处理后的校验结果
     */
    private ValidationResult processMatchResults(ValidationResult result, 
                                               List<String> exactMatches, 
                                               List<String> similarMatches, 
                                               String inputOrgCode) {
        
        // 优先处理精确匹配
        if (!exactMatches.isEmpty()) {
            if (exactMatches.size() == 1) {
                // 单个精确匹配
                String matchedKeyno = exactMatches.get(0);
                FourCodeInfo fourCodeInfo = fourCodeService.getFourCodeByKeyno(matchedKeyno);
                
                result.setMatchedKeyno(matchedKeyno);
                result.setMatchedFourCode(fourCodeInfo);
                result.setMatchType(ValidationResult.MatchType.EXACT);
                result.setValidationStatus(ValidationResult.ValidationStatus.SUCCESS);
                result.setConfidenceScore(1.0);
                result.setMessage("精确匹配成功");
                
                logger.info("精确匹配成功: keyno={}", matchedKeyno);
            } else {
                // 多个精确匹配
                result.setMatchType(ValidationResult.MatchType.MULTIPLE);
                result.setValidationStatus(ValidationResult.ValidationStatus.NEED_MANUAL);
                result.setCandidateKeynos(exactMatches);
                result.setConfidenceScore(0.8);
                result.setMessage("找到多个精确匹配的keyno，需要人工处理");
                
                logger.warn("多个精确匹配: keynos={}, 输入代码={}", exactMatches, inputOrgCode);
                // TODO: 根据文档要求，多个匹配时打印结果
                printMultipleMatchResults(exactMatches, inputOrgCode, "精确匹配");
            }
        }
        // 处理相似匹配
        else if (!similarMatches.isEmpty()) {
            if (similarMatches.size() == 1) {
                // 单个相似匹配
                String matchedKeyno = similarMatches.get(0);
                FourCodeInfo fourCodeInfo = fourCodeService.getFourCodeByKeyno(matchedKeyno);
                
                result.setMatchedKeyno(matchedKeyno);
                result.setMatchedFourCode(fourCodeInfo);
                result.setMatchType(ValidationResult.MatchType.SIMILAR);
                result.setValidationStatus(ValidationResult.ValidationStatus.SUCCESS);
                result.setConfidenceScore(0.7);
                result.setMessage("相似匹配成功");
                
                logger.info("相似匹配成功: keyno={}", matchedKeyno);
            } else {
                // 多个相似匹配
                result.setMatchType(ValidationResult.MatchType.MULTIPLE);
                result.setValidationStatus(ValidationResult.ValidationStatus.NEED_MANUAL);
                result.setCandidateKeynos(similarMatches);
                result.setConfidenceScore(0.5);
                result.setMessage("找到多个相似匹配的keyno，需要转入NLP处理");
                
                logger.warn("多个相似匹配: keynos={}, 输入代码={}", similarMatches, inputOrgCode);
                // TODO: 根据文档要求，多个匹配时打印结果
                printMultipleMatchResults(similarMatches, inputOrgCode, "相似匹配");
            }
        }
        // 没有找到匹配
        else {
            result.setMatchType(ValidationResult.MatchType.NOT_FOUND);
            result.setValidationStatus(ValidationResult.ValidationStatus.FAILED);
            result.setConfidenceScore(0.0);
            result.setMessage("未找到匹配的四码信息");
            
            logger.info("未找到匹配: 输入代码={}", inputOrgCode);
        }
        
        return result;
    }
    
    /**
     * 打印多个匹配结果
     * TODO: 根据文档要求，当多个keyno都匹配时打印结果
     * @param matchedKeynos 匹配的keyno列表
     * @param inputOrgCode 输入的组织机构代码
     * @param matchType 匹配类型
     */
    private void printMultipleMatchResults(List<String> matchedKeynos, String inputOrgCode, String matchType) {
        logger.warn("=== 多个{}结果 ===", matchType);
        logger.warn("输入组织机构代码: {}", inputOrgCode);
        logger.warn("匹配到的keyno数量: {}", matchedKeynos.size());
        
        for (int i = 0; i < matchedKeynos.size(); i++) {
            String keyno = matchedKeynos.get(i);
            FourCodeInfo fourCodeInfo = fourCodeService.getFourCodeByKeyno(keyno);
            
            logger.warn("候选{}. keyno: {}", (i + 1), keyno);
            if (fourCodeInfo != null) {
                logger.warn("  - 统一信用代码: {}", fourCodeInfo.getCreditCode());
                logger.warn("  - 组织机构代码: {}", fourCodeInfo.getOrgCode());
                logger.warn("  - 工商注册号: {}", fourCodeInfo.getBusinessRegNo());
                logger.warn("  - 纳税人识别号: {}", fourCodeInfo.getTaxNo());
            }
        }
        logger.warn("=== 多个{}结果结束 ===", matchType);
    }
    
    /**
     * 输出校验结果
     * @param result 校验结果
     */
    private void outputValidationResult(ValidationResult result) {
        String timestamp = java.time.LocalDateTime.now().toString();
        
        System.out.println("=== 校验结果 ===");
        System.out.println("处理时间: " + timestamp);
        System.out.println("记录ID: " + result.getOriginalCompanyInfo().getRecordId());
        System.out.println("公司名称: " + result.getOriginalCompanyInfo().getCompanyName());
        System.out.println("原始组织机构代码: " + result.getOriginalCompanyInfo().getOriginalOrgCode());
        System.out.println("校验状态: " + result.getValidationStatus());
        System.out.println("匹配类型: " + result.getMatchType());
        System.out.println("置信度: " + String.format("%.2f", result.getConfidenceScore()));
        
        if (result.getMatchedKeyno() != null) {
            System.out.println("匹配的keyno: " + result.getMatchedKeyno());
        }
        
        if (result.getMatchedFourCode() != null) {
            System.out.println("匹配的四码信息:");
            System.out.println("  - 统一信用代码: " + result.getMatchedFourCode().getCreditCode());
            System.out.println("  - 组织机构代码: " + result.getMatchedFourCode().getOrgCode());
            System.out.println("  - 工商注册号: " + result.getMatchedFourCode().getBusinessRegNo());
            System.out.println("  - 纳税人识别号: " + result.getMatchedFourCode().getTaxNo());
        }
        
        if (result.getCandidateKeynos() != null && !result.getCandidateKeynos().isEmpty()) {
            System.out.println("候选keyno列表: " + result.getCandidateKeynos());
        }
        
        System.out.println("处理消息: " + result.getMessage());
        System.out.println("================");
        System.out.println();
        
        // 记录到日志
        logger.info("校验结果输出: 公司={}, 状态={}, 匹配类型={}, keyno={}", 
                   result.getOriginalCompanyInfo().getCompanyName(),
                   result.getValidationStatus(),
                   result.getMatchType(),
                   result.getMatchedKeyno());
    }
}
