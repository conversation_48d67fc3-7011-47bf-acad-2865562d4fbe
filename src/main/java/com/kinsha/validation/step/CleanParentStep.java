package com.kinsha.validation.step;

import org.apache.flink.streaming.api.datastream.DataStream;

import java.util.List;

/**
 * 清洗步骤父类
 * 定义数据清洗的标准流程
 * 
 * <AUTHOR>
 * @version 1.0
 * @param <T> 输入数据类型
 * @param <R> 输出数据类型
 */
public abstract class CleanParentStep<T, R> {
    
    /**
     * 处理清洗数据的主流程
     * @param input 输入数据流
     */
    public void process(DataStream<T> input) {
        // 1. 转换为生产对象
        DataStream<R> prodObjectStream = transformBaseObjectToProdObjectFlatMap(input);
        
        // 2. 查询法院信息
        DataStream<R> courtInfoStream = queryCourtInfoFunction(prodObjectStream);
        
        // 3. 转换为列表形式进行批量处理
        DataStream<List<R>> listStream = convertToListStream(courtInfoStream);
        
        // 4. 推送风险消息
        pushRiskMessageFunction(listStream);
        
        // 5. 保存业务数据
        saveBusinessDataFunction(listStream);
        
        // 6. 清洗并保存详细数据
        cleanAndSaveDetailDataFunction(courtInfoStream);
        
        // 7. 清洗并刷新统计数据
        cleanAndRefreshCountDataFunction(courtInfoStream);
    }
    
    /**
     * 将基础对象转换为生产对象
     * @param dataStream 输入数据流
     * @return 转换后的数据流
     */
    protected abstract DataStream<R> transformBaseObjectToProdObjectFlatMap(DataStream<T> dataStream);
    
    /**
     * 查询法院信息
     * @param dataStream 数据流
     * @return 包含法院信息的数据流
     */
    protected abstract DataStream<R> queryCourtInfoFunction(DataStream<R> dataStream);
    
    /**
     * 推送风险消息
     * @param dataStream 数据流列表
     */
    protected abstract void pushRiskMessageFunction(DataStream<List<R>> dataStream);
    
    /**
     * 保存业务数据
     * @param dataStream 数据流列表
     */
    protected abstract void saveBusinessDataFunction(DataStream<List<R>> dataStream);
    
    /**
     * 清洗并保存详细数据
     * @param dataStream 数据流
     */
    protected abstract void cleanAndSaveDetailDataFunction(DataStream<R> dataStream);
    
    /**
     * 清洗并刷新统计数据
     * @param dataStream 数据流
     */
    protected abstract void cleanAndRefreshCountDataFunction(DataStream<R> dataStream);
    
    /**
     * 将单个数据流转换为列表数据流
     * TODO: 实现数据流到列表的转换逻辑
     * @param dataStream 单个数据流
     * @return 列表数据流
     */
    private DataStream<List<R>> convertToListStream(DataStream<R> dataStream) {
        // TODO: 实现转换逻辑，可以使用窗口或其他方式
        return null;
    }
}
