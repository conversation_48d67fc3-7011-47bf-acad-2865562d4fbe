package com.kinsha.validation.step;

import org.apache.flink.streaming.api.datastream.DataStream;

/**
 * 爬虫处理步骤父类
 * 定义爬虫数据处理的标准流程
 * 
 * <AUTHOR>
 * @version 1.0
 * @param <T> 输入数据类型
 * @param <R> 输出数据类型
 */
public abstract class SpiderParentStep<T, R> {
    
    /**
     * 处理爬虫数据的主流程
     * @param input 输入数据流
     */
    public void process(DataStream<String> input) {
        // 1. 字符串转对象
        DataStream<T> objectStream = transformSpiderStringToObjectFlatMap(input);
        
        // 2. 保存到MongoDB（可选）
        saveSpiderToMongoDB(objectStream);
        
        // 3. 字段清洗
        DataStream<T> cleanedStream = cleanSpiderFieldFlatMap(objectStream);
        
        // 4. 查询公司KeyNo
        DataStream<T> companyKeyNoStream = queryCompanyKeyNoFunction(cleanedStream);
        
        // 5. 查询个人KeyNo
        DataStream<T> personKeyNoStream = queryPersonKeyNoFunction(companyKeyNoStream);
        
        // 6. 合并爬虫和DAP数据
        DataStream<T> mergedStream = mergeSpiderAndDapFunction(personKeyNoStream);
        
        // 7. 清洗步骤
        cleanStep(mergedStream);
        
        // 8. 保存到基础数据库
        saveBaseDB(mergedStream);
    }
    
    /**
     * 将字符串转换为对象
     * @param dataStream 输入字符串流
     * @return 转换后的对象流
     */
    protected abstract DataStream<T> transformSpiderStringToObjectFlatMap(DataStream<String> dataStream);
    
    /**
     * 保存到MongoDB
     * @param dataStream 数据流
     */
    protected abstract void saveSpiderToMongoDB(DataStream<T> dataStream);
    
    /**
     * 清洗爬虫字段
     * @param dataStream 数据流
     * @return 清洗后的数据流
     */
    protected abstract DataStream<T> cleanSpiderFieldFlatMap(DataStream<T> dataStream);
    
    /**
     * 查询公司KeyNo
     * @param dataStream 数据流
     * @return 包含公司KeyNo的数据流
     */
    protected abstract DataStream<T> queryCompanyKeyNoFunction(DataStream<T> dataStream);
    
    /**
     * 查询个人KeyNo
     * @param dataStream 数据流
     * @return 包含个人KeyNo的数据流
     */
    protected abstract DataStream<T> queryPersonKeyNoFunction(DataStream<T> dataStream);
    
    /**
     * 合并爬虫和DAP数据
     * @param dataStream 数据流
     * @return 合并后的数据流
     */
    protected abstract DataStream<T> mergeSpiderAndDapFunction(DataStream<T> dataStream);
    
    /**
     * 清洗步骤
     * @param dataStream 数据流
     */
    protected abstract void cleanStep(DataStream<T> dataStream);
    
    /**
     * 保存到基础数据库
     * @param dataStream 数据流
     */
    protected abstract void saveBaseDB(DataStream<T> dataStream);
}
