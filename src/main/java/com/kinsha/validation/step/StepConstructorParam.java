package com.kinsha.validation.step;

import lombok.Builder;
import lombok.Data;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

/**
 * Step构造参数
 * 用于在各个Step之间传递公共参数
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
@Builder
public class StepConstructorParam {
    
    /** Flink执行环境 */
    private StreamExecutionEnvironment env;
    
    /** 配置文件参数 */
    private ParameterTool propertiesParams;
    
    /** DMP参数 */
    private ParameterTool dmpParams;
    
    /** 核心线程池大小 */
    private int corePoolSize;
}
