package com.kinsha.validation.step;

import org.apache.flink.streaming.api.datastream.DataStream;

/**
 * 主流程父类
 * 定义标准的数据处理流程步骤
 * 
 * <AUTHOR>
 * @version 1.0
 */
public abstract class MainParentStep {
    
    /**
     * 处理主流程
     * 按照标准流程执行：获取数据源 → 处理步骤
     */
    public void process() {
        // 1. 获取爬虫数据源
        DataStream<String> spiderSourceData = getSpiderSourceData();
        if (spiderSourceData != null) {
            spiderStep(spiderSourceData);
        }
        
        // 2. 获取DAP数据源
        DataStream<String> dapSourceData = getDapSourceData();
        if (dapSourceData != null) {
            dapStep(dapSourceData);
        }
    }
    
    /**
     * 获取爬虫数据源
     * @return 爬虫数据流
     */
    protected abstract DataStream<String> getSpiderSourceData();
    
    /**
     * 获取DAP数据源
     * @return DAP数据流
     */
    protected abstract DataStream<String> getDapSourceData();
    
    /**
     * 爬虫数据处理步骤
     * @param input 输入数据流
     */
    protected abstract void spiderStep(DataStream<String> input);
    
    /**
     * DAP数据处理步骤
     * @param input 输入数据流
     */
    protected abstract void dapStep(DataStream<String> input);
}
