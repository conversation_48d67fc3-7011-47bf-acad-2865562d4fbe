package com.kinsha.validation.step;

import com.kinsha.validation.model.CompanyInfo;
import com.kinsha.validation.model.ValidationResult;
import com.kinsha.validation.operator.DataPreprocessOperator;
import com.kinsha.validation.operator.JsonParseOperator;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 被执行人公司与组织机构校验规则爬虫处理步骤
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class SpiderStep extends SpiderParentStep<CompanyInfo, ValidationResult> {
    
    private static final Logger logger = LoggerFactory.getLogger(SpiderStep.class);
    private StepConstructorParam stepConstructorParam;
    
    public SpiderStep(StepConstructorParam stepConstructorParam) {
        this.stepConstructorParam = stepConstructorParam;
    }
    
    @Override
    protected DataStream<CompanyInfo> transformSpiderStringToObjectFlatMap(DataStream<String> dataStream) {
        logger.info("步骤1: 将字符串转换为CompanyInfo对象");

        return dataStream
                .flatMap(new JsonParseOperator())
                .name("TransformStringToCompanyInfo");
    }
    
    @Override
    protected void saveSpiderToMongoDB(DataStream<CompanyInfo> dataStream) {
        logger.info("步骤2: 保存到MongoDB (当前跳过)");
        
        // TODO: 被执行人公司校验规则暂时不需要保存到MongoDB
        // 如果后期需要，可以在这里添加MongoDB保存逻辑
    }
    
    @Override
    protected DataStream<CompanyInfo> cleanSpiderFieldFlatMap(DataStream<CompanyInfo> dataStream) {
        logger.info("步骤3: 清洗爬虫字段 - 数据预处理");

        return dataStream
                .map(new DataPreprocessOperator())
                .name("CleanSpiderFields");
    }
    
    @Override
    protected DataStream<CompanyInfo> queryCompanyKeyNoFunction(DataStream<CompanyInfo> dataStream) {
        logger.info("步骤4: 查询公司KeyNo - 核心校验逻辑");

        // TODO: 这里应该实现根据公司名称查询keyno的逻辑
        // 当前只是标记，实际的校验逻辑在CleanStep中实现
        return dataStream.name("QueryCompanyKeyNo");
    }
    
    @Override
    protected DataStream<CompanyInfo> queryPersonKeyNoFunction(DataStream<CompanyInfo> dataStream) {
        logger.info("步骤5: 查询个人KeyNo (当前业务不需要)");
        
        // TODO: 被执行人公司校验规则不涉及个人KeyNo查询，直接返回
        return dataStream;
    }
    
    @Override
    protected DataStream<CompanyInfo> mergeSpiderAndDapFunction(DataStream<CompanyInfo> dataStream) {
        logger.info("步骤6: 合并爬虫和DAP数据 (当前业务不需要)");
        
        // TODO: 被执行人公司校验规则暂时不需要合并DAP数据，直接返回
        return dataStream;
    }
    
    @Override
    protected void cleanStep(DataStream<CompanyInfo> dataStream) {
        logger.info("步骤7: 执行清洗步骤 - 核心校验逻辑");
        
        // 执行清洗步骤，这里是核心的校验逻辑
        new CleanStep(stepConstructorParam.getCorePoolSize()).process(dataStream);
    }
    
    @Override
    protected void saveBaseDB(DataStream<CompanyInfo> dataStream) {
        logger.info("步骤8: 保存到基础数据库 (当前跳过)");
        
        // TODO: 被执行人公司校验规则暂时不需要保存到基础数据库
        // 如果后期需要，可以在这里添加数据库保存逻辑
    }
    

}
