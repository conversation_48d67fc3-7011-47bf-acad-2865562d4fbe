package com.kinsha.validation.step;

import com.kinsha.validation.model.CompanyInfo;
import com.kinsha.validation.model.ValidationResult;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.regex.Pattern;

/**
 * 被执行人公司与组织机构校验规则爬虫处理步骤
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class SpiderStep extends SpiderParentStep<CompanyInfo, ValidationResult> {
    
    private static final Logger logger = LoggerFactory.getLogger(SpiderStep.class);
    private StepConstructorParam stepConstructorParam;
    
    // 弱智数字模式
    private static final Pattern WEAK_NUMBER_PATTERN = Pattern.compile("^(0+|1+0+|123456|*********)$");
    
    // 纯中文模式
    private static final Pattern CHINESE_PATTERN = Pattern.compile("^[\\u4e00-\\u9fa5]+$");
    
    public SpiderStep(StepConstructorParam stepConstructorParam) {
        this.stepConstructorParam = stepConstructorParam;
    }
    
    @Override
    protected DataStream<CompanyInfo> transformSpiderStringToObjectFlatMap(DataStream<String> dataStream) {
        logger.info("步骤1: 将字符串转换为CompanyInfo对象");
        
        return dataStream.flatMap(new FlatMapFunction<String, CompanyInfo>() {
            @Override
            public void flatMap(String jsonStr, Collector<CompanyInfo> collector) throws Exception {
                try {
                    // TODO: 这里简化处理JSON解析，实际应该使用Jackson等JSON库
                    // 解析JSON字符串为CompanyInfo对象
                    CompanyInfo companyInfo = parseJsonToCompanyInfo(jsonStr);
                    if (companyInfo != null) {
                        collector.collect(companyInfo);
                    }
                } catch (Exception e) {
                    logger.error("解析JSON字符串失败: {}", jsonStr, e);
                }
            }
        }).name("TransformStringToCompanyInfo");
    }
    
    @Override
    protected void saveSpiderToMongoDB(DataStream<CompanyInfo> dataStream) {
        logger.info("步骤2: 保存到MongoDB (当前跳过)");
        
        // TODO: 被执行人公司校验规则暂时不需要保存到MongoDB
        // 如果后期需要，可以在这里添加MongoDB保存逻辑
    }
    
    @Override
    protected DataStream<CompanyInfo> cleanSpiderFieldFlatMap(DataStream<CompanyInfo> dataStream) {
        logger.info("步骤3: 清洗爬虫字段 - 数据预处理");
        
        return dataStream.map(new MapFunction<CompanyInfo, CompanyInfo>() {
            @Override
            public CompanyInfo map(CompanyInfo companyInfo) throws Exception {
                // 数据预处理：清洗空格、标点，统一大小写，过滤无效数据
                CompanyInfo processedInfo = new CompanyInfo();
                processedInfo.setCompanyName(companyInfo.getCompanyName());
                processedInfo.setDataSource(companyInfo.getDataSource());
                processedInfo.setRecordId(companyInfo.getRecordId());
                
                // 预处理组织机构代码
                String processedOrgCode = preprocessOrgCode(companyInfo.getOriginalOrgCode());
                processedInfo.setOriginalOrgCode(processedOrgCode);
                
                logger.debug("数据预处理: {} -> {}", companyInfo.getOriginalOrgCode(), processedOrgCode);
                return processedInfo;
            }
        }).name("CleanSpiderFields");
    }
    
    @Override
    protected DataStream<CompanyInfo> queryCompanyKeyNoFunction(DataStream<CompanyInfo> dataStream) {
        logger.info("步骤4: 查询公司KeyNo - 核心校验逻辑");
        
        return dataStream.map(new MapFunction<CompanyInfo, CompanyInfo>() {
            @Override
            public CompanyInfo map(CompanyInfo companyInfo) throws Exception {
                // TODO: 这里应该实现根据公司名称查询keyno的逻辑
                // 当前只是标记，实际的校验逻辑在CleanStep中实现
                logger.debug("查询公司KeyNo: {}", companyInfo.getCompanyName());
                return companyInfo;
            }
        }).name("QueryCompanyKeyNo");
    }
    
    @Override
    protected DataStream<CompanyInfo> queryPersonKeyNoFunction(DataStream<CompanyInfo> dataStream) {
        logger.info("步骤5: 查询个人KeyNo (当前业务不需要)");
        
        // TODO: 被执行人公司校验规则不涉及个人KeyNo查询，直接返回
        return dataStream;
    }
    
    @Override
    protected DataStream<CompanyInfo> mergeSpiderAndDapFunction(DataStream<CompanyInfo> dataStream) {
        logger.info("步骤6: 合并爬虫和DAP数据 (当前业务不需要)");
        
        // TODO: 被执行人公司校验规则暂时不需要合并DAP数据，直接返回
        return dataStream;
    }
    
    @Override
    protected void cleanStep(DataStream<CompanyInfo> dataStream) {
        logger.info("步骤7: 执行清洗步骤 - 核心校验逻辑");
        
        // 执行清洗步骤，这里是核心的校验逻辑
        new CleanStep(stepConstructorParam.getCorePoolSize()).process(dataStream);
    }
    
    @Override
    protected void saveBaseDB(DataStream<CompanyInfo> dataStream) {
        logger.info("步骤8: 保存到基础数据库 (当前跳过)");
        
        // TODO: 被执行人公司校验规则暂时不需要保存到基础数据库
        // 如果后期需要，可以在这里添加数据库保存逻辑
    }
    
    /**
     * 解析JSON字符串为CompanyInfo对象
     * TODO: 简化实现，实际应该使用Jackson等JSON库
     * @param jsonStr JSON字符串
     * @return CompanyInfo对象
     */
    private CompanyInfo parseJsonToCompanyInfo(String jsonStr) {
        try {
            // 简单的JSON解析实现
            String companyName = extractJsonValue(jsonStr, "companyName");
            String originalOrgCode = extractJsonValue(jsonStr, "originalOrgCode");
            String dataSource = extractJsonValue(jsonStr, "dataSource");
            String recordId = extractJsonValue(jsonStr, "recordId");
            
            return new CompanyInfo(companyName, originalOrgCode, dataSource, recordId);
        } catch (Exception e) {
            logger.error("解析JSON失败: {}", jsonStr, e);
            return null;
        }
    }
    
    /**
     * 从JSON字符串中提取指定字段的值
     * TODO: 简化实现，实际应该使用JSON库
     * @param jsonStr JSON字符串
     * @param fieldName 字段名
     * @return 字段值
     */
    private String extractJsonValue(String jsonStr, String fieldName) {
        String pattern = "\"" + fieldName + "\":\"([^\"]*?)\"";
        java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
        java.util.regex.Matcher m = p.matcher(jsonStr);
        if (m.find()) {
            String value = m.group(1);
            return "null".equals(value) ? null : value;
        }
        return "";
    }
    
    /**
     * 预处理组织机构代码
     * 1. 清洗空格、标点符号
     * 2. 统一大小写
     * 3. 过滤无效数据
     * 
     * @param originalCode 原始代码
     * @return 处理后的代码
     */
    private String preprocessOrgCode(String originalCode) {
        // 1. 空值处理
        if (originalCode == null) {
            logger.debug("组织机构代码为null，返回空字符串");
            return "";
        }
        
        // 2. 清洗空格和标点符号
        String cleanedCode = cleanSpacesAndPunctuation(originalCode);
        
        // 3. 统一大小写（转为大写）
        String upperCaseCode = cleanedCode.toUpperCase();
        
        // 4. 无效数据过滤
        String validatedCode = filterInvalidData(upperCaseCode);
        
        return validatedCode;
    }
    
    /**
     * 清洗空格和标点符号
     * @param code 原始代码
     * @return 清洗后的代码
     */
    private String cleanSpacesAndPunctuation(String code) {
        if (code == null || code.trim().isEmpty()) {
            return "";
        }
        
        // 移除空格
        String result = code.replaceAll("\\s+", "");
        
        // 移除常见标点符号（保留*用于脱敏标识）
        result = result.replaceAll("[，。、；：""''（）【】《》〈〉{}\\[\\]()\"',;:.!?]", "");
        
        // 移除连字符（但保留在组织机构代码中的-）
        // 只移除明显多余的连字符
        result = result.replaceAll("^-+|-+$", ""); // 移除开头和结尾的连字符
        
        logger.debug("清洗标点符号: {} -> {}", code, result);
        return result;
    }
    
    /**
     * 过滤无效数据
     * 根据文档方案2：长度<=6的认定为无效code，替换为空
     * 
     * @param code 清洗后的代码
     * @return 验证后的代码
     */
    private String filterInvalidData(String code) {
        if (code == null || code.trim().isEmpty()) {
            return "";
        }
        
        String trimmedCode = code.trim();
        
        // 1. 长度检查：<=6的认定为无效
        if (trimmedCode.length() <= 6) {
            logger.debug("代码长度过短(<=6)，认定为无效: {}", trimmedCode);
            return "";
        }
        
        // 2. 全零检查
        if (trimmedCode.matches("^0+$")) {
            logger.debug("代码为全零，认定为无效: {}", trimmedCode);
            return "";
        }
        
        // 3. 弱智数字检查
        String codeWithoutMask = trimmedCode.replaceAll("\\*", ""); // 临时移除*进行检查
        if (WEAK_NUMBER_PATTERN.matcher(codeWithoutMask).matches()) {
            logger.debug("代码为弱智数字，认定为无效: {}", trimmedCode);
            return "";
        }
        
        // 4. 纯中文检查
        if (CHINESE_PATTERN.matcher(trimmedCode).matches()) {
            logger.debug("代码为纯中文，认定为无效: {}", trimmedCode);
            return "";
        }
        
        // 5. 特殊无效值检查
        if ("不提供".equals(trimmedCode) || "无".equals(trimmedCode) || "NULL".equals(trimmedCode)) {
            logger.debug("代码为特殊无效值，认定为无效: {}", trimmedCode);
            return "";
        }
        
        logger.debug("代码验证通过: {}", trimmedCode);
        return trimmedCode;
    }
}
