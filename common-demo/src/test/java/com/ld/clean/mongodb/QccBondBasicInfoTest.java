package com.ld.clean.mongodb;

import com.ld.clean.pojo.mo.basicinfo.BasicInfo;
import com.ld.clean.pojo.mo.basicinfo.ConversionTerms;
import com.ld.clean.pojo.mo.basicinfo.IssueInfo;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.List;

/**
 * 债券基本信息test
 * <AUTHOR>
 * @date 2021年8月25日 19点32分
 */
@Data
@Document(collection = "Qcc_Bond_BasicInfo")
public class QccBondBasicInfoTest {
    private String _id;

    @Field("DataStatus")
    private int dataStatus;

    @Field("RelatedSec")
    private String relatedSec;

    @Field("Symbol")
    private String symbol;

    @Field("DerivedSymbol")
    private String derivedSymbol;

    @Field("Type")
    private int type;

    /**
     * 基本信息
     */
    @Field("BasicInfo")
    private BasicInfo basicInfo;

    /**
     * 发行信息
     */
    @Field("IssueInfo")
    private IssueInfo issueInfo;

    /**
     * 转债条款
     */
    @Field("ConversionTerms")
    private List<ConversionTerms> conversionTerms;
}
