package com.ld.clean.mongodb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.util.TypeUtils;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

public class JsonObjectTest {
    public static void main(String[] args) {
        Test test = new Test();
//        test.setVal1(1);
        test.setVal2(0.1D);
        test.setTest1(new Test1());
//        test.setVal3("test");
        JSONObject parentDoc = JSONObject.parseObject(JSON.toJSONString(test));
        Map<String,String> fieldMap = new HashMap<>();
        TypeUtils.computeGetters(Test.class,null).stream().forEach(x->{
            fieldMap.put(x.field.getName(),x.fieldType.getTypeName());
            System.out.println(x.field.getName()+x.fieldType.getTypeName());
            if(x.fieldType.getTypeName().contains("$")){
                TypeUtils.computeGetters(x.fieldClass,null).forEach(y ->{
                    fieldMap.put(y.field.getName(),y.fieldType.getTypeName());
                });
            }
        });
        parentDoc.forEach((key, value) ->{
            value.getClass();
        });
    }



    @Data
    static
    class Test{
        private Integer val1;
        private Double val2;
        private String val3;
        private Test1 test1;
    }

    @Data
    static
    class Test1{
        private String id;
    }
}
