package com.ld.clean.mongodb;

import com.ld.clean.mongo.BaseMongoEnum;
import com.ld.clean.mongo.BaseMongoTemplate;
import com.ld.clean.pojo.common.escount.CompCountDetailMo;
import com.ld.clean.pojo.mo.basicinfo.BasicInfo;
import com.ld.clean.pojo.mo.basicinfo.ConversionTerms;
import com.ld.clean.utils.DateUtil;
import com.ld.clean.utils.MongodbUtil;
import com.mongodb.BasicDBObject;
import com.mongodb.client.result.UpdateResult;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.junit.Test;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

public class MongodbTest {
    private final static MongoTemplate mongoTemplate =  BaseMongoTemplate.getInstance(BaseMongoEnum.PROD_FINANCIAL_DB);
    public static final String ID_VAL = "_id";

    @Test
    public void test1() {
        /** 召回部分字段 */
        Query query1=new Query();
        query1.addCriteria(Criteria.where("_id").is("test_zhaoyong"));
        query1.fields().include(ID_VAL);
        query1.fields().include("IssueInfo");
        QccBondBasicInfoTest result = mongoTemplate.findOne(query1,QccBondBasicInfoTest.class);
        System.out.println("query:"+result);

        /** 根据id更新第一层的值 */
        QccBondBasicInfoTest qccBondBasicInfoTest = new QccBondBasicInfoTest();
        qccBondBasicInfoTest.setSymbol("test111");
        Long val = MongodbUtil.updateById(BaseMongoEnum.PROD_FINANCIAL_DB,"test_zhaoyong",qccBondBasicInfoTest);
        System.out.println("val:"+val);

        /** 根据id更新嵌套子文档的内容 */
        BasicInfo basicInfo = new BasicInfo();
        basicInfo.setCompName("test");
        basicInfo.setBondType("2");
        qccBondBasicInfoTest.setBasicInfo(basicInfo);
        Long val1 = MongodbUtil.updateById(BaseMongoEnum.PROD_FINANCIAL_DB,"test_zhaoyong",qccBondBasicInfoTest);
        System.out.println(val1);

        /** 添加list */
        List<ConversionTerms> list = new ArrayList<>();
        ConversionTerms obj1 = new ConversionTerms();
        obj1.setId("test1");
        obj1.setRatio("1");
//        obj1.setBondNewPrice(0.8d);
        list.add(obj1);
        ConversionTerms obj2 = new ConversionTerms();
        obj2.setId("test2");
        obj2.setRatio("2");
//        obj2.setBondNewPrice(0.2d);
        list.add(obj2);
        Update update2 = new Update();
        update2.set("ConversionTerms",list);
        Long val2 = MongodbUtil.updateFieldsById(BaseMongoEnum.PROD_FINANCIAL_DB,"test_zhaoyong",update2, QccBondBasicInfoTest.class);
        System.out.println(val2);

        /** 更新list中的一行中的某值 */
        Query query = Query.query(Criteria.where("_id").is("test_zhaoyong")
                .and("ConversionTerms._id").is("1"));
        Update update3 = new Update();
        update3.set("ConversionTerms.$.BondNewPrice", 0.4d);
        mongoTemplate.upsert(query, update3, QccBondBasicInfoTest.class);

        /** 插入list中的一行，如果要更新，先删除再插入 */
        Query query4 = Query.query(Criteria.where("_id").is("test_zhaoyong"));
        Update update4 = new Update();
        ConversionTerms obj4 = new ConversionTerms();
        obj4.setId("test3");
        obj4.setRatio("2");
//        obj4.setBondNewPrice(0.6d);
        update4.push("ConversionTerms",obj4);
        mongoTemplate.updateFirst(query4, update4, QccBondBasicInfoTest.class);

        /** 删除list中的一行 */
        Query query5 = Query.query(Criteria.where("_id").is("test_zhaoyong"));
        Update update5 = new Update();
        update5.pull("ConversionTerms",new BasicDBObject("_id","test1"));
        mongoTemplate.updateFirst(query5, update5, QccBondBasicInfoTest.class);
    }

    @Test
    public void test3() throws InterruptedException {
        ExecutorService executor = Executors.newFixedThreadPool(30); // 创建固定大小的线程池
        for(int i=0;i<100;i++){
            int finalI = i;
            executor.submit(()->{
                test2(finalI);
            });
        }
        executor.shutdown();;
        executor.awaitTermination(10, TimeUnit.SECONDS);
    }

    public void test2(int value){
        MongoTemplate template = BaseMongoTemplate.getInstance(BaseMongoEnum.BASE_CLEAN_DATA);
        System.out.println(value +"时间："+ LocalDateTime.now());
        String keyno = "7691cbd0181925e6886d5db81d3ef58f";
        Query query1 = Query.query(Criteria.where("_id").is(keyno));
        Update delUpdate = new Update();
        delUpdate.pull("CommonList",new BasicDBObject("Key",38));
        template.upsert(query1, delUpdate, CompCountDetailMo.class);

        Query query = Query.query(Criteria.where("_id").is(keyno));
        query.addCriteria(Criteria.where("CommonList.Key").is(36));

        Long count = template.exactCount(query,CompCountDetailMo.class);
        System.out.println(count);
        Update update = new Update();
        update.addToSet("CommonList.$.Value", value);
        update.set("Remark", "test");
        UpdateResult updateResult = template.upsert(query, update, CompCountDetailMo.class);
    }
}
