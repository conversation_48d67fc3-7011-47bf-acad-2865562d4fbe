package com.ld.clean.jdbc;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;

public class DbUtil {

    public static final String URL = "************************************************************";
    public static final String USER = "base_app_admin";
    public static final String PASSWORD = "RDpNySxG1nTSI9pxRNJa";

    public static final String URL1 = "******************************************************************";
    public static final String USER1 = "qcc_app_user";
    public static final String PASSWORD1 = "QiChaCha_4567PaSS";

    public static void main(String[] args) throws Exception {
        //1.加载驱动程序
        Class.forName("com.mysql.cj.jdbc.Driver");
        //2. 获得数据库连接
        Connection conn = DriverManager.getConnection(URL1, USER1, PASSWORD1);
        //3.操作数据库，实现增删改查
        Statement stmt = conn.createStatement();
        ResultSet rs = stmt.executeQuery("select * from risk_supervise_punish_administrative_sync_qh limit 10");
        System.out.println("结果："+rs);
        //如果有数据，rs.next()返回true
        while(rs.next()){
            System.out.println(rs.getString("id"));
        }

//        //1.加载驱动程序
//        Class.forName("com.mysql.cj.jdbc.Driver");
//        //2. 获得数据库连接
//        Connection conn1 = DriverManager.getConnection(URL, USER, PASSWORD);
//        //3.操作数据库，实现增删改查
//        Statement stmt1 = conn1.createStatement();
//        ResultSet rs1 = stmt1.executeQuery("select * from aa_llh limit 10");
//        System.out.println("结果1："+rs1);
    }
}