# ç»´åº¦ä¸­æå
clean.app.name=æ¨¡æ¿æ¨¡å

# ç»´åº¦è±æåï¼ä»pom.xmlä¸­èªå¨è·å
clean.app.id=@project.artifactId@

# ä»¥ä¸topicågroup idä»ä¸ºåèç¨ä¾ï¼å¯ä»¥è°ç¨ï¼å®éåºç¨è¯·æ ¹æ®å®éæåµåä¿®æ¹

# ç¬è«topic å group_id
spider.metrics.topic=base_investment_institutions_new_test
spider.kafka.group.id=group_base_new_spider

# dapå¹³å°topic å group_id
dap.metrics.topic=base_investment_institutions_new_test
dap.kafka.group.id=group_base_new_spider

# æ¸æ´ä¸å¡é»è¾topic å group_id
clean.metrics.topic=base_investment_institutions_new_test
clean.kafka.group.id=group_base_new_spider

# stream
stream.parallelism=1
# 5åéè§¦åä¸æ¬¡
stream.checkpoint.interval=300000
stream.checkpoint.enable=true
stream.checkpoint.type=fs
# checkpoint ä¿å­æ¶é´é»è®¤ï¼20åé
stream.checkpoint.timeout=1200000