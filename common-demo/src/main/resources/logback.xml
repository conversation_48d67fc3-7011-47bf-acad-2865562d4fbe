<?xml version="1.0" encoding="UTF-8" ?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <conversionRule conversionWord="m" converterClass="com.tuandai.log.logback.converter.TagMessageConverter"/>
    <!--控制台打印-->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder
                class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符 -->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="com.ld.clean.mapper" level="ERROR"/>

    <!--日志级别-->
    <root level="INFO">
        <appender-ref ref="STDOUT" />
    </root>
</configuration>