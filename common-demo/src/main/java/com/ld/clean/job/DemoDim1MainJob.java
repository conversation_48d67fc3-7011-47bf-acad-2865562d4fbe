package com.ld.clean.job;

import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.dim.dim1.step.MainStep;
import com.ld.clean.step.StepConstructorParam;
import com.ld.clean.utils.CheckPointUtil;
import com.ld.clean.utils.ExecutionEnvUtil;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

/**
 * 模板任务
 */
public class DemoDim1MainJob {
    public static void main(String[] args) throws Exception {
        ParameterTool propertiesParams = ExecutionEnvUtil.PARAMETER_TOOL;
        StreamExecutionEnvironment env = CheckPointUtil.setCheckpointConfig(ExecutionEnvUtil.prepare(propertiesParams), propertiesParams);
        ParameterTool dmpParams = ParameterTool.fromArgs(args);
        new MainStep(StepConstructorParam.builder().env(env).propertiesParams(propertiesParams).dmpParams(dmpParams).build()).process();
        env.execute(ApplicationCoreContextManager.getAppId());
    }
}