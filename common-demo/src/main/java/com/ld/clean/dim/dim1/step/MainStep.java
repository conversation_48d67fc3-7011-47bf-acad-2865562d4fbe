package com.ld.clean.dim.dim1.step;

import com.ld.clean.constants.MyConstants;
import com.ld.clean.constants.PropertiesConstants;
import com.ld.clean.kafka.CleanFlinkKafkaConsumer;
import com.ld.clean.kafka.KafkaHelper;
import com.ld.clean.step.MainParentStep;
import com.ld.clean.step.StepConstructorParam;
import org.apache.flink.streaming.api.datastream.DataStream;

/**
 * 主流程
 */
public class MainStep extends MainParentStep {
    private StepConstructorParam stepConstructorParam;

    public MainStep(StepConstructorParam stepConstructorParam) throws Exception {
        this.stepConstructorParam = stepConstructorParam;
    }

    @Override
    protected DataStream<String> getSpiderSourceData() {
        String topic = stepConstructorParam.getPropertiesParams().get(PropertiesConstants.SPIDER_METRICS_TOPIC);
        String group = stepConstructorParam.getPropertiesParams().get(PropertiesConstants.SPIDER_KAFKA_GROUP_ID);
       /* return stepConstructorParam.getEnv().addSource(KafkaHelper.buildAuthKafkaSource(
                CleanFlinkKafkaConsumer.builder().topic(topic).groupId(group).build())
                .setCommitOffsetsOnCheckpoints(true))
                .setParallelism(1);*/
        return null;
    }

    @Override
    protected DataStream<String> getDapSourceData() {
        String topic = stepConstructorParam.getPropertiesParams().get(PropertiesConstants.DAP_METRICS_TOPIC);
        String group = stepConstructorParam.getPropertiesParams().get(PropertiesConstants.DAP_KAFKA_GROUP_ID);
//        return stepConstructorParam.getEnv().addSource(KafkaHelper.buildAuthKafkaSource(
//                CleanFlinkKafkaConsumer.builder().topic(topic).groupId(group).build())
//                .setCommitOffsetsOnCheckpoints(true))
//                .setParallelism(1);
        return null;
    }

    @Override
    protected void dapStep(DataStream<String> input) {
        stepConstructorParam.getEnv().setParallelism(1);
        new DapStep().process(input);
    }

    @Override
    protected void spiderStep(DataStream<String> input) {
        int spiderProcessParallelism = stepConstructorParam.getDmpParams().getInt(MyConstants.SPIDER_PROCESS_PARALLELISM,
                stepConstructorParam.getPropertiesParams().getInt(PropertiesConstants.STREAM_PARALLELISM));
        stepConstructorParam.getEnv().setParallelism(spiderProcessParallelism);
        int corePoolSize = stepConstructorParam.getDmpParams().getInt(MyConstants.CORE_POOL_SIZE, MyConstants.CORE_POOL_SIZE_DEFAULT);
        stepConstructorParam.setCorePoolSize(corePoolSize);
        new SpiderStep(stepConstructorParam).process(input);
    }
}
