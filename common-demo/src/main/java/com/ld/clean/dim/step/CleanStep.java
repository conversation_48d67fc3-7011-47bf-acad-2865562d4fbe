package com.ld.clean.dim.step;

import com.ld.clean.step.CleanParentStep;
import org.apache.flink.streaming.api.datastream.DataStream;

import java.util.List;

/**
 * 清洗逻辑
 */
public class CleanStep extends CleanParentStep<Object, Object> {
    private int corePoolSize;

    public CleanStep(int corePoolSize) {
        this.corePoolSize = corePoolSize;
    }

    @Override
    protected DataStream<Object> transformBaseObjectToProdObjectFlatMap(DataStream<Object> dataStream) {
        return null;
    }

    @Override
    protected DataStream<Object> queryCourtInfoFunction(DataStream<Object> dataStream) {
        return null;
    }

    @Override
    protected void pushRiskMessageFunction(DataStream<List<Object>> dataStream) {

    }

    @Override
    protected void saveBusinessDataFunction(DataStream<List<Object>> dataStream) {

    }

    @Override
    protected void cleanAndSaveDetailDataFunction(DataStream<Object> dataStream) {

    }

    @Override
    protected void cleanAndRefreshCountDataFunction(DataStream<Object> dataStream) {

    }
}
