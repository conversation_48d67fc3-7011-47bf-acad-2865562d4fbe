package com.ld.clean.dim.dim1.async.clean;

import com.ld.clean.parent.AsyncCleanParentFunction;

import java.util.List;

/**
 * 基础数据插入数据库
 */
public class InsertBaseDataTidbFunction extends AsyncCleanParentFunction<List<Object>, Boolean> {
    private static final long serialVersionUID = -4208390498631676331L;

    public InsertBaseDataTidbFunction(int corePoolSize) {
        super(corePoolSize);
    }

    /**
     * 仅作为样例参照
     */
    @Override
    public Boolean invoke(List<Object> list) throws Exception {
        // 数据入库
        return true;
    }
}
