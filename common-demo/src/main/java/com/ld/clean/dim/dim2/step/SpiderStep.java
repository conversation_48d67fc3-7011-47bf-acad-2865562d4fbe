package com.ld.clean.dim.dim2.step;

import com.ld.clean.datasteam.AsyncCleanDataSteam;
import com.ld.clean.datasteam.WindowCleanDataStream;
import com.ld.clean.dim.dim1.async.clean.InsertBaseDataTidbFunction;
import com.ld.clean.dim.dim2.async.spider.SpiderCleanAsync;
import com.ld.clean.step.SpiderParentStep;
import com.ld.clean.step.StepConstructorParam;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.util.Collector;

import java.util.List;

/**
 * 爬虫字段清洗步骤
 */
public class SpiderStep extends SpiderParentStep<Object, Object> {
    private StepConstructorParam stepConstructorParam;

    public SpiderStep(StepConstructorParam stepConstructorParam) {
        this.stepConstructorParam = stepConstructorParam;
    }


    @Override
    protected DataStream<Object> transformSpiderStringToObjectFlatMap(DataStream<String> dataStream) {
        // 原始数据转换处理
        return dataStream.flatMap(new FlatMapFunction<String, Object>() {
            @Override
            public void flatMap(String s, Collector<Object> collector) throws Exception {
                
            }
        });
    }

    @Override
    protected void saveSpiderToMongoDB(DataStream<Object> dataStream) {

    }

    @Override
    protected DataStream<Object> cleanSpiderFieldFlatMap(DataStream<Object> dataStream) {
        return AsyncCleanDataSteam.orderedWait(
                dataStream, new SpiderCleanAsync(stepConstructorParam.getCorePoolSize())).name("SpiderCleanAsync");

    }

    @Override
    protected DataStream<Object> queryCompanyKeyNoFunction(DataStream<Object> dataStream) {
        return null;
    }

    @Override
    protected DataStream<Object> queryPersonKeyNoFunction(DataStream<Object> dataStream) {
        return null;
    }

    @Override
    protected DataStream<Object> mergeSpiderAndDapFunction(DataStream<Object> dataStream) {
        return null;
    }

    @Override
    protected void cleanStep(DataStream<Object> dataStream) {
        new CleanStep(stepConstructorParam.getCorePoolSize()).process(dataStream);
    }

    @Override
    protected void saveBaseDB(DataStream<Object> dataStream) {
        DataStream<List<Object>> list = WindowCleanDataStream.toDataStreamList(dataStream);
        AsyncCleanDataSteam.orderedWait(list, new InsertBaseDataTidbFunction(stepConstructorParam.getCorePoolSize())).name("SpiderCleanAsync");
    }
}
