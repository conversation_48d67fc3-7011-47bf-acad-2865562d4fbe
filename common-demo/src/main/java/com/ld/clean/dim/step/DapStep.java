package com.ld.clean.dim.step;

import com.ld.clean.step.DapParentStep;
import org.apache.flink.streaming.api.datastream.DataStream;

/**
 * dap清洗
 */
public class DapStep extends DapParentStep<Object> {
    int corePoolSize = 1;

    @Override
    protected DataStream<Object> transformDapStringToObjecFlatMap(DataStream<String> dataStream) {
        return null;
    }

    @Override
    protected DataStream<Object> mergeDapAndBaseFunction(DataStream<Object> dataStream) {
        return null;
    }

    @Override
    protected void cleanStep(DataStream<Object> dataStream) {

    }

    @Override
    protected void saveBaseDB(DataStream<Object> dataStream) {

    }
}
