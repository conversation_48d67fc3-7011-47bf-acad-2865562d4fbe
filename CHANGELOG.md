# 修改日志

## 版本 1.0.0 (2025-01-31)

### 新增功能
- ✅ 实现基于Flink的公司与组织机构代码校验系统
- ✅ 完成MainJob主程序入口点设计
- ✅ 实现Source数据源组件（测试数据）
- ✅ 实现数据预处理StepFunction
- ✅ 实现核心校验逻辑StepFunction  
- ✅ 实现Sink结果输出组件
- ✅ 建立完整的数据模型体系

### 修改文件列表

#### 项目配置文件
- **新增**: `pom.xml` - Maven项目配置，添加Flink依赖

#### 数据模型 (model包)
- **新增**: `CompanyInfo.java` - 公司信息输入数据模型
- **新增**: `ValidationResult.java` - 校验结果输出数据模型  
- **新增**: `FourCodeInfo.java` - 四码信息数据模型

#### 数据源 (source包)
- **新增**: `CompanyInfoSource.java` - 公司信息数据源，提供15个测试用例

#### 处理函数 (function包)
- **新增**: `DataPreprocessFunction.java` - 数据预处理函数
  - 实现空格、标点符号清洗
  - 实现大小写统一转换
  - 实现无效数据过滤（长度≤6、弱智数字、纯中文等）
- **新增**: `CompanyValidationFunction.java` - 核心校验函数
  - 实现keyno查询逻辑
  - 实现四码校验规则
  - 实现相似匹配算法
  - 实现多keyno冲突处理

#### 业务服务 (service包)
- **新增**: `KeynoService.java` - Keyno查询服务（测试数据）
- **新增**: `FourCodeService.java` - 四码查询服务（测试数据）

#### 输出组件 (sink包)
- **新增**: `ValidationResultSink.java` - 校验结果输出组件（控制台输出）

#### 主程序
- **新增**: `CompanyOrgValidationJob.java` - Flink主程序入口

#### 文档
- **新增**: `README.md` - 项目说明文档
- **新增**: `CHANGELOG.md` - 修改日志文档

### 技术实现要点

#### 数据预处理规则
- 清洗空格和标点符号（保留*用于脱敏标识）
- 统一转换为大写
- 过滤长度≤6的无效代码
- 过滤弱智数字模式：`^(0+|1+0+|123456|*********)$`
- 过滤纯中文代码
- 过滤特殊无效值："不提供"、"无"、"NULL"

#### 相似匹配算法
- 去除输入代码中的脱敏字符（*）
- 检查剩余字符是否能在目标代码中按顺序找到
- 支持部分字符缺失的匹配场景
- 示例：`32058400****0923` 匹配 `320584000120923`

#### 校验优先级
1. 精确匹配（组织机构代码完全一致）
2. 相似匹配（去除脱敏字符后顺序匹配）
3. 多匹配处理（输出所有候选结果）

### 测试数据覆盖场景
1. 精确匹配 - 组织机构代码完全一致
2. 精确匹配 - 统一信用代码完全一致
3. 相似匹配 - 统一信用代码部分脱敏
4. 相似匹配 - 组织机构代码部分脱敏
5. 无法匹配 - 代码不存在
6. 无效数据 - 代码长度过短
7. 无效数据 - 纯数字弱智代码
8. 无效数据 - 全零代码
9. 多个匹配 - 可能匹配多个keyno
10. 名称不完整 - 缺少地区信息
11. 空值测试
12. null值测试
13. 包含特殊字符
14. 大小写混合
15. 长度边界测试

### TODO标记位置
所有测试数据和临时实现都已标记TODO注释，便于后期替换：

#### 数据源相关TODO
- `CompanyInfoSource.java` - 测试数据源需要替换为真实数据源
- `KeynoService.java` - 测试数据映射需要替换为数据库查询
- `FourCodeService.java` - 测试数据映射需要替换为数据库查询

#### 输出相关TODO  
- `ValidationResultSink.java` - 控制台输出需要扩展为数据库、文件、Kafka等

#### 配置相关TODO
- `CompanyOrgValidationJob.java` - 并行度等配置需要根据实际需求调整

### 修改原因
基于企查查被执行人公司与组织机构校验规则文档需求，实现完整的Flink数据处理流水线，解决公司名称与组织机构代码匹配的核心业务问题。

### 下一步计划
1. 集成真实数据源和数据库连接
2. 优化相似匹配算法性能
3. 实现完整的输出存储方案
4. 添加监控和统计功能
5. 性能调优和容错机制完善
