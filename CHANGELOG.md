# 修改日志

## 版本 3.0.0 (2025-01-31)

### 代码重构 - 工具类和算子分离
- ✅ 将所有工具类和业务代码从Step中抽离
- ✅ 创建独立的Flink算子类，提高代码复用性
- ✅ 建立完整的工具类体系，保持代码整洁和规范
- ✅ Step类只负责流程控制，具体逻辑由算子和工具类实现

### 新增工具类 (utils包)
- **新增**: `JsonUtils.java` - JSON处理工具类，提供JSON解析和转换功能
- **新增**: `DataCleanUtils.java` - 数据清洗工具类，提供数据预处理和验证功能
- **新增**: `SimilarMatchUtils.java` - 相似匹配工具类，提供相似匹配算法实现
- **新增**: `ValidationResultUtils.java` - 校验结果工具类，提供结果处理和输出功能

### 新增算子类 (operator包)
- **新增**: `JsonParseOperator.java` - JSON解析算子，负责字符串转CompanyInfo对象
- **新增**: `DataPreprocessOperator.java` - 数据预处理算子，负责数据清洗和验证
- **新增**: `CompanyValidationOperator.java` - 公司校验算子，负责核心校验逻辑
- **新增**: `ValidationResultOutputOperator.java` - 结果输出算子，负责校验结果输出

### 重构Step类
- **重构**: `SpiderStep.java` - 移除内部业务逻辑，使用算子类实现
- **重构**: `CleanStep.java` - 移除内部业务逻辑，使用算子类实现
- **重构**: `FourCodeInfo.java` - 相似匹配逻辑移到SimilarMatchUtils工具类

## 版本 2.0.0 (2025-01-31)

### 重大重构 - 按照标准清洗流程重新实现
- ✅ 完全按照common-demo标准流程重构代码架构
- ✅ 实现标准的MainJob → MainStep → SpiderStep/DapStep → CleanStep流程
- ✅ 所有步骤都保留，即使用不到也有空实现
- ✅ 严格遵循企查查清洗流程规范

### 新增标准流程父类
- **新增**: `MainParentStep.java` - 主流程父类，定义标准数据处理流程
- **新增**: `SpiderParentStep.java` - 爬虫处理父类，定义8个标准步骤
- **新增**: `CleanParentStep.java` - 清洗步骤父类，定义6个标准步骤
- **新增**: `DapParentStep.java` - DAP处理父类，定义4个标准步骤
- **新增**: `StepConstructorParam.java` - 步骤构造参数，使用Lombok Builder模式

### 重构具体实现类
- **重构**: `MainStep.java` - 主流程实现，继承MainParentStep
- **重构**: `SpiderStep.java` - 爬虫处理实现，继承SpiderParentStep，包含完整8步骤
- **新增**: `CleanStep.java` - 清洗步骤实现，继承CleanParentStep，包含核心校验逻辑
- **新增**: `DapStep.java` - DAP处理实现，继承DapParentStep，当前业务不需要

### 重构主程序
- **重构**: `CompanyOrgValidationMainJob.java` - 按照标准流程重新实现主程序
- **删除**: 旧的`CompanyOrgValidationJob.java`、Source、Function、Sink等非标准组件

## 版本 1.0.0 (2025-01-31)

### 新增功能
- ✅ 实现基于Flink的公司与组织机构代码校验系统
- ✅ 完成MainJob主程序入口点设计
- ✅ 实现Source数据源组件（测试数据）
- ✅ 实现数据预处理StepFunction
- ✅ 实现核心校验逻辑StepFunction  
- ✅ 实现Sink结果输出组件
- ✅ 建立完整的数据模型体系

### 标准流程步骤详解

#### SpiderStep标准8步骤实现
1. **transformSpiderStringToObjectFlatMap()** - JSON字符串转CompanyInfo对象
2. **saveSpiderToMongoDB()** - 保存到MongoDB（当前跳过）
3. **cleanSpiderFieldFlatMap()** - 数据预处理和字段清洗
4. **queryCompanyKeyNoFunction()** - 查询公司KeyNo（标记步骤）
5. **queryPersonKeyNoFunction()** - 查询个人KeyNo（当前跳过）
6. **mergeSpiderAndDapFunction()** - 合并爬虫和DAP数据（当前跳过）
7. **cleanStep()** - 调用CleanStep执行核心校验逻辑
8. **saveBaseDB()** - 保存到基础数据库（当前跳过）

#### CleanStep标准6步骤实现
1. **transformBaseObjectToProdObjectFlatMap()** - 核心校验逻辑，CompanyInfo转ValidationResult
2. **queryCourtInfoFunction()** - 查询法院信息（当前跳过）
3. **pushRiskMessageFunction()** - 推送风险消息（当前跳过）
4. **saveBusinessDataFunction()** - 保存业务数据（当前跳过）
5. **cleanAndSaveDetailDataFunction()** - 输出校验结果到控制台
6. **cleanAndRefreshCountDataFunction()** - 统计数据处理（当前跳过）

#### DapStep标准4步骤实现
1. **transformDapStringToObjecFlatMap()** - DAP字符串转对象（当前跳过）
2. **mergeDapAndBaseFunction()** - 合并DAP和基础数据（当前跳过）
3. **cleanStep()** - DAP清洗步骤（当前跳过）
4. **saveBaseDB()** - 保存DAP数据（当前跳过）

### 修改文件列表

#### 标准流程父类 (step包)
- **新增**: `StepConstructorParam.java` - 步骤构造参数，使用Lombok Builder
- **新增**: `MainParentStep.java` - 主流程父类
- **新增**: `SpiderParentStep.java` - 爬虫处理父类
- **新增**: `CleanParentStep.java` - 清洗步骤父类
- **新增**: `DapParentStep.java` - DAP处理父类

#### 标准流程实现类 (step包)
- **重构**: `MainStep.java` - 主流程实现，继承MainParentStep
- **重构**: `SpiderStep.java` - 爬虫处理实现，包含完整8步骤流程
- **新增**: `CleanStep.java` - 清洗步骤实现，包含核心校验逻辑
- **新增**: `DapStep.java` - DAP处理实现，所有步骤当前跳过

#### 主程序 (job包)
- **重构**: `CompanyOrgValidationMainJob.java` - 按照标准流程重新实现

#### 数据模型和服务 (保持不变)
- **保留**: `CompanyInfo.java` - 公司信息模型
- **保留**: `ValidationResult.java` - 校验结果模型
- **保留**: `FourCodeInfo.java` - 四码信息模型
- **保留**: `KeynoService.java` - Keyno查询服务
- **保留**: `FourCodeService.java` - 四码查询服务

#### 删除的非标准组件
- **删除**: `CompanyOrgValidationJob.java` - 旧主程序
- **删除**: `CompanyInfoSource.java` - 非标准数据源
- **删除**: `DataPreprocessFunction.java` - 非标准处理函数
- **删除**: `CompanyValidationFunction.java` - 非标准校验函数
- **删除**: `ValidationResultSink.java` - 非标准输出组件

#### 配置文件
- **更新**: `pom.xml` - 添加Lombok依赖，更新主类路径
- **更新**: `README.md` - 更新项目结构和使用说明
- **更新**: `CHANGELOG.md` - 记录重构变更

### 技术实现要点

#### 数据预处理规则
- 清洗空格和标点符号（保留*用于脱敏标识）
- 统一转换为大写
- 过滤长度≤6的无效代码
- 过滤弱智数字模式：`^(0+|1+0+|123456|*********)$`
- 过滤纯中文代码
- 过滤特殊无效值："不提供"、"无"、"NULL"

#### 相似匹配算法
- 去除输入代码中的脱敏字符（*）
- 检查剩余字符是否能在目标代码中按顺序找到
- 支持部分字符缺失的匹配场景
- 示例：`32058400****0923` 匹配 `320584000120923`

#### 校验优先级
1. 精确匹配（组织机构代码完全一致）
2. 相似匹配（去除脱敏字符后顺序匹配）
3. 多匹配处理（输出所有候选结果）

### 测试数据覆盖场景
1. 精确匹配 - 组织机构代码完全一致
2. 精确匹配 - 统一信用代码完全一致
3. 相似匹配 - 统一信用代码部分脱敏
4. 相似匹配 - 组织机构代码部分脱敏
5. 无法匹配 - 代码不存在
6. 无效数据 - 代码长度过短
7. 无效数据 - 纯数字弱智代码
8. 无效数据 - 全零代码
9. 多个匹配 - 可能匹配多个keyno
10. 名称不完整 - 缺少地区信息
11. 空值测试
12. null值测试
13. 包含特殊字符
14. 大小写混合
15. 长度边界测试

### TODO标记位置
所有测试数据和临时实现都已标记TODO注释，便于后期替换：

#### 数据源相关TODO
- `CompanyInfoSource.java` - 测试数据源需要替换为真实数据源
- `KeynoService.java` - 测试数据映射需要替换为数据库查询
- `FourCodeService.java` - 测试数据映射需要替换为数据库查询

#### 输出相关TODO  
- `ValidationResultSink.java` - 控制台输出需要扩展为数据库、文件、Kafka等

#### 配置相关TODO
- `CompanyOrgValidationJob.java` - 并行度等配置需要根据实际需求调整

### 重构原因
1. **标准化要求**：严格按照common-demo标准清洗流程实现
2. **流程完整性**：确保所有标准步骤都存在，即使暂时用不到
3. **代码规范性**：遵循企查查清洗流程的架构规范
4. **可扩展性**：为后期功能扩展提供标准化基础

### 核心业务逻辑保持不变
- 数据预处理逻辑（清洗、过滤、验证）
- 四码校验规则（精确匹配、相似匹配）
- 多keyno冲突处理和结果输出
- 15个测试用例覆盖各种场景

### 下一步计划
1. 根据TODO标记替换测试数据为真实数据源
2. 实现跳过的步骤（如MongoDB保存、数据库存储等）
3. 优化相似匹配算法性能
4. 添加监控和统计功能
5. 性能调优和容错机制完善
